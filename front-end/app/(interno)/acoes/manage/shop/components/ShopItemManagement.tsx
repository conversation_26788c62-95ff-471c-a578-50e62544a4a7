"use client"

import { useState, useEffect } from 'react'
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { IItem, IGetShopItemsResponse, IRemoveItemsFromShopRequest, IUpdateItemsRequest } from "@/services/SHOP/interfaces"
import ShopAPI from "@/services/SHOP/api"
import { Search, Globe, Building, Loader2, Trash2, Package, Edit, Save, X } from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"

export default function ShopItemManagement() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingItems, setIsLoadingItems] = useState(false)
  const [globalItems, setGlobalItems] = useState<IItem[]>([])
  const [localItems, setLocalItems] = useState<IItem[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedShop, setSelectedShop] = useState<'GLOBAL' | 'LOCAL'>('GLOBAL')
  const [userContractShopId, setUserContractShopId] = useState<string | null>(null)
  const [itemsToRemove, setItemsToRemove] = useState<string[]>([])
  const [editingItem, setEditingItem] = useState<string | null>(null)
  const [editForm, setEditForm] = useState<{ stock: number; currentPrice: number }>({ stock: 0, currentPrice: 0 })

  // Load user's contract shop ID
  useEffect(() => {
    const loadUserContractShop = async () => {
      try {
        // TODO: Replace with actual API call to get user's contract shop ID
        const userInfo = localStorage.getItem('user')
        if (userInfo) {
          const user = JSON.parse(userInfo)
          setUserContractShopId(user.contractShopId || null)
        }
      } catch (error) {
        console.error('Error loading user contract shop:', error)
      }
    }

    loadUserContractShop()
  }, [])

  // Load shop items
  useEffect(() => {
    const loadShopItems = async () => {
      setIsLoadingItems(true)
      try {
        const response: IGetShopItemsResponse = await ShopAPI.getShopItems()
        setGlobalItems(response.GLOBAL || [])
        setLocalItems(response.LOCAL || [])
      } catch (error) {
        console.error('Error loading shop items:', error)
        toast({
          title: "Erro",
          description: "Erro ao carregar itens das lojas.",
          variant: "destructive"
        })
      } finally {
        setIsLoadingItems(false)
      }
    }

    loadShopItems()
  }, [toast])

  // Helper function to get the correct ID from an item
  const getItemId = (item: IItem): string => {
    return item._id || item.id || ''
  }

  // Get current items based on selected shop
  const getCurrentItems = (): IItem[] => {
    return selectedShop === 'GLOBAL' ? globalItems : localItems
  }

  // Filter items based on search
  const getFilteredItems = (): IItem[] => {
    const items = getCurrentItems()
    if (!searchTerm) return items

    return items.filter(item =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }

  const handleItemToggle = (itemId: string) => {
    setItemsToRemove(prev => {
      const newSelection = prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
      return newSelection
    })
  }

  const handleRemoveItems = async () => {
    if (itemsToRemove.length === 0) {
      toast({
        title: "Aviso",
        description: "Selecione pelo menos um item para remover.",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      const request: IRemoveItemsFromShopRequest = {
        itemIds: itemsToRemove
      }
      
      await ShopAPI.removeItemsFromShop(request)
      
      toast({
        title: "Sucesso!",
        description: `${itemsToRemove.length} item(s) removido(s) da loja ${selectedShop === 'GLOBAL' ? 'global' : 'local'}.`,
        variant: "default"
      })

      // Refresh items
      const response: IGetShopItemsResponse = await ShopAPI.getShopItems()
      setGlobalItems(response.GLOBAL || [])
      setLocalItems(response.LOCAL || [])
      
      // Clear selection
      setItemsToRemove([])
      
    } catch (error) {
      console.error('Error removing items from shop:', error)
      toast({
        title: "Erro",
        description: "Erro ao remover itens da loja. Tente novamente.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditItem = (item: IItem) => {
    setEditingItem(getItemId(item))
    setEditForm({
      stock: item.stock || 0,
      currentPrice: item.currentPrice || 0
    })
  }

  const handleSaveEdit = async () => {
    if (!editingItem) return

    setIsLoading(true)
    try {
      const request: IUpdateItemsRequest = {
        itemUpdates: [{
          id: editingItem,
          data: {
            stock: editForm.stock,
            currentPrice: editForm.currentPrice
          }
        }],
        validateOnly: false
      }
      
      await ShopAPI.updateItems(request)
      
      toast({
        title: "Sucesso!",
        description: "Item atualizado com sucesso.",
        variant: "default"
      })

      // Refresh items
      const response: IGetShopItemsResponse = await ShopAPI.getShopItems()
      setGlobalItems(response.GLOBAL || [])
      setLocalItems(response.LOCAL || [])
      
      // Clear editing state
      setEditingItem(null)
      setEditForm({ stock: 0, currentPrice: 0 })
      
    } catch (error) {
      console.error('Error updating item:', error)
      toast({
        title: "Erro",
        description: "Erro ao atualizar item. Tente novamente.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setEditingItem(null)
    setEditForm({ stock: 0, currentPrice: 0 })
  }

  const filteredItems = getFilteredItems()

  return (
    <Card className="p-6 bg-blue-950/10 border-blue-500/20">
      <div className="space-y-6">
        {/* Header */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-white">Gerenciar Itens das Lojas</h3>
          <p className="text-blue-300 text-sm">
            Visualize, remova e reabasteça itens das lojas global e local.
          </p>
        </div>

        {/* Shop Selection */}
        <div className="space-y-2">
          <Label className="text-blue-300">Loja</Label>
          <Select value={selectedShop} onValueChange={(value: 'GLOBAL' | 'LOCAL') => setSelectedShop(value)}>
            <SelectTrigger className="bg-blue-950/30 border-blue-500/30 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-blue-950 border-blue-500">
              <SelectItem value="GLOBAL">
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  Loja Global
                </div>
              </SelectItem>
              <SelectItem value="LOCAL">
                <div className="flex items-center gap-2">
                  <Building className="w-4 h-4" />
                  Loja Local (Seu Contrato)
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          {selectedShop === 'LOCAL' && !userContractShopId && (
            <p className="text-orange-400 text-sm">
              ⚠️ Não foi possível carregar o ID da loja do seu contrato. Entre em contato com o suporte.
            </p>
          )}
        </div>

        {/* Search */}
        <div className="space-y-2">
          <Label className="text-blue-300">Buscar Itens</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400 w-4 h-4" />
            <Input
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Buscar por nome, descrição ou categoria..."
              className="pl-10 bg-blue-950/30 border-blue-500/30 text-white"
            />
          </div>
        </div>

        {/* Items List */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-blue-300">Itens da Loja</Label>
            <span className="text-blue-400 text-sm">
              {itemsToRemove.length} de {filteredItems.length} selecionado(s) para remoção
            </span>
          </div>
          
          {isLoadingItems ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="animate-spin h-6 w-6 text-blue-400" />
              <span className="ml-2 text-blue-300">Carregando itens...</span>
            </div>
          ) : (
            <div className="max-h-96 overflow-y-auto space-y-2 border border-blue-500/30 rounded-lg p-4 bg-blue-950/20">
              {filteredItems.length === 0 ? (
                <p className="text-blue-400 text-center py-4">Nenhum item encontrado</p>
              ) : (
                <>
                  {filteredItems.map((item) => {
                    const itemId = getItemId(item)
                    const isEditing = editingItem === itemId
                    
                    return (
                      <div
                        key={itemId}
                        className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                          itemsToRemove.includes(itemId)
                            ? 'bg-red-600/30 border-red-500/50'
                            : 'bg-blue-950/20 border-blue-500/20 hover:bg-blue-950/40'
                        }`}
                      >
                        <div className="flex-1">
                          <h4 className="font-medium text-white">{item.name}</h4>
                          <p className="text-blue-300 text-sm">{item.description}</p>
                          <div className="flex items-center gap-4 mt-1">
                            <span className="text-blue-400 text-xs">{item.category}</span>
                            <span className="text-blue-400 text-xs">{item.type}</span>
                            <span className="text-green-400 text-xs">{item.currentPrice} ATOMs</span>
                            <span className={`text-xs ${item.stock > 0 ? 'text-green-400' : 'text-red-400'}`}>
                              Estoque: {item.stock}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {/* Edit Button */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditItem(item)}
                            className="border-blue-500/30 text-blue-300 hover:bg-blue-950/30"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          
                          {/* Remove Toggle */}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleItemToggle(itemId)}
                            className={`${
                              itemsToRemove.includes(itemId)
                                ? 'border-red-500/30 text-red-300 hover:bg-red-950/30'
                                : 'border-blue-500/30 text-blue-300 hover:bg-blue-950/30'
                            }`}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    )
                  })}
                </>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-blue-500/30">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setItemsToRemove([])
              setSearchTerm('')
            }}
            className="border-blue-500/30 text-blue-300 hover:bg-blue-950/30"
          >
            Limpar Seleção
          </Button>
          <Button
            onClick={handleRemoveItems}
            disabled={isLoading || itemsToRemove.length === 0}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Removendo...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                Remover Itens
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Edit Item Dialog */}
      <Dialog open={!!editingItem} onOpenChange={() => handleCancelEdit()}>
        <DialogContent className="bg-blue-950/90 border-blue-500/30">
          <DialogHeader>
            <DialogTitle className="text-white">Editar Item</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-blue-300">Estoque</Label>
              <Input
                type="number"
                value={editForm.stock}
                onChange={(e) => setEditForm(prev => ({ ...prev, stock: parseInt(e.target.value) || 0 }))}
                className="bg-blue-950/30 border-blue-500/30 text-white"
                min="0"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-blue-300">Preço Atual (ATOMs)</Label>
              <Input
                type="number"
                value={editForm.currentPrice}
                onChange={(e) => setEditForm(prev => ({ ...prev, currentPrice: parseInt(e.target.value) || 0 }))}
                className="bg-blue-950/30 border-blue-500/30 text-white"
                min="0"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={handleCancelEdit}
                className="border-blue-500/30 text-blue-300 hover:bg-blue-950/30"
              >
                <X className="w-4 h-4 mr-2" />
                Cancelar
              </Button>
              <Button
                onClick={handleSaveEdit}
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Salvar
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
