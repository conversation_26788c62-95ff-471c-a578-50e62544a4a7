"use client"

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BookOpen, Plus, Loader2, AlertCircle, Brain, Star, FolderPlus, X, Search, Filter, XCircle, ChevronDown, Trash2 } from 'lucide-react';
import { Deck } from '../utils/dataMapping';
import { CreateFlashcardModal } from './CreateFlashcardModal';
import { EditFlashcardModal } from './EditFlashcardModal';
import { FlashcardCardComponent } from './FlashcardCardComponent';
import { CustomSelect } from '@/components/ui/CustomSelect';
import FlashcardController from '@/services/CDS/api/FlashcardController';
import { Flashcard, updateDeck_ModifyFlashcardsRequest, DeleteFlashcardsRequest } from '@/services/CDS/interfaces/flashcard';

interface FlashcardsTabViewProps {
    decks: Deck[];
    deckAtual: Deck | null;
    onSetDeckAtual: (deck: Deck) => void;
    onSetShowCreateFlashcard: (show: boolean) => void;
    onSetActiveTab: (tab: 'decks' | 'flashcards') => void;
    onFlashcardsCreated: () => void;
}

export function FlashcardsTabView({
    decks,
    deckAtual,
    onSetDeckAtual,
    onSetShowCreateFlashcard,
    onSetActiveTab,
    onFlashcardsCreated
}: FlashcardsTabViewProps) {
    const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editingFlashcard, setEditingFlashcard] = useState<Flashcard | null>(null);
    const [showAssignModal, setShowAssignModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [selectedFlashcards, setSelectedFlashcards] = useState<string[]>([]);
    const [selectedDeckId, setSelectedDeckId] = useState<string>('');
    const [assignLoading, setAssignLoading] = useState(false);
    const [deleteLoading, setDeleteLoading] = useState(false);
    
    // Filter states
    const [searchContent, setSearchContent] = useState('');
    const [selectedTag, setSelectedTag] = useState<string>('');
    const [selectedDifficulty, setSelectedDifficulty] = useState<number | ''>('');
    const [showFilters, setShowFilters] = useState(false);

    // Load flashcards on component mount
    useEffect(() => {
        loadFlashcards();
    }, []);

    // Debug log for flashcards state changes
    useEffect(() => {
        console.log('Flashcards state updated:', flashcards.length, flashcards);
    }, [flashcards]);

    // Get unique tags from flashcards
    const availableTags = useMemo(() => {
        const tags = new Set<string>();
        flashcards.forEach(flashcard => {
            if (flashcard.tags) {
                flashcard.tags.forEach(tag => tags.add(tag));
            }
        });
        return Array.from(tags).sort();
    }, [flashcards]);

    // Create options for CustomSelect components
    const tagOptions = useMemo(() => {
        const options = [
            { value: '', label: 'Todas as tags' },
            ...availableTags.map(tag => ({ value: tag, label: tag }))
        ];
        console.log('Tag options generated:', options);
        return options;
    }, [availableTags]);

    const difficultyOptions = useMemo(() => {
        const options = [
            { value: '', label: 'Todas as dificuldades' },
            { value: 1, label: 'Fácil' },
            { value: 2, label: 'Médio' },
            { value: 3, label: 'Difícil' }
        ];
        console.log('Difficulty options generated:', options);
        return options;
    }, []);

    // Filter flashcards based on search criteria
    const filteredFlashcards = useMemo(() => {
        return flashcards.filter(flashcard => {
            // Content filter (search in question and answer)
            const contentMatch = !searchContent || 
                flashcard.question.toLowerCase().includes(searchContent.toLowerCase()) ||
                flashcard.answer.toLowerCase().includes(searchContent.toLowerCase());

            // Tag filter
            const tagMatch = !selectedTag || 
                (flashcard.tags && flashcard.tags.includes(selectedTag));

            // Difficulty filter
            const difficultyMatch = !selectedDifficulty || 
                flashcard.difficulty === selectedDifficulty;

            return contentMatch && tagMatch && difficultyMatch;
        });
    }, [flashcards, searchContent, selectedTag, selectedDifficulty]);

    // Clear all filters
    const clearFilters = () => {
        setSearchContent('');
        setSelectedTag('');
        setSelectedDifficulty('');
    };

    // Check if any filters are active
    const hasActiveFilters = searchContent || selectedTag || selectedDifficulty;

    const loadFlashcards = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await FlashcardController.getSelfFlashcards(1, 50); // Get first 50 flashcards
            console.log('Flashcards response:', response); // Debug log
            
            // Handle both response formats: direct array or wrapped in data property
            if (Array.isArray(response)) {
                console.log('Setting flashcards from direct array:', response.length);
                setFlashcards(response);
            } else if (response && response.data && Array.isArray(response.data)) {
                console.log('Setting flashcards from response.data:', response.data.length);
                setFlashcards(response.data);
            } else {
                console.log('No flashcards found in response:', response);
                setFlashcards([]);
            }
        } catch (err) {
            console.error('Error loading flashcards:', err);
            setError('Erro ao carregar flashcards');
        } finally {
            setLoading(false);
        }
    };

    const handleFlashcardCreated = () => {
        loadFlashcards(); // Reload flashcards
        onFlashcardsCreated(); // Notify parent component
        setShowCreateModal(false);
    };

    const handleEditFlashcard = (flashcard: Flashcard) => {
        setEditingFlashcard(flashcard);
        setShowEditModal(true);
    };

    const handleFlashcardUpdated = () => {
        loadFlashcards(); // Reload flashcards
        onFlashcardsCreated(); // Notify parent component
        setShowEditModal(false);
        setEditingFlashcard(null);
    };

    const handleDeleteFlashcards = async () => {
        if (selectedFlashcards.length === 0) {
            setError('Nenhum flashcard selecionado para exclusão');
            return;
        }

        setDeleteLoading(true);
        setError(null);

        try {
            const deleteRequest: DeleteFlashcardsRequest = {
                flashcardIds: selectedFlashcards
            };

            const response = await FlashcardController.deleteFlashcards(deleteRequest);
            
            if (response) {
                // Success - reset selections and close modal
                setSelectedFlashcards([]);
                setShowDeleteModal(false);
                loadFlashcards(); // Reload flashcards
                onFlashcardsCreated(); // Refresh decks in parent
                console.log('Flashcards deleted successfully');
            } else {
                throw new Error('Falha ao excluir flashcards');
            }
        } catch (err) {
            console.error('Error deleting flashcards:', err);
            setError('Erro ao excluir flashcards');
        } finally {
            setDeleteLoading(false);
        }
    };

    const toggleFlashcardSelection = (flashcardId: string) => {
        setSelectedFlashcards(prev => 
            prev.includes(flashcardId) 
                ? prev.filter(id => id !== flashcardId)
                : [...prev, flashcardId]
        );
    };

    const handleAssignToDeck = async () => {
        if (!selectedDeckId || selectedFlashcards.length === 0) {
            setError('Por favor, selecione um deck e pelo menos um flashcard');
            return;
        }

        setAssignLoading(true);
        setError(null);

        try {
            const updateRequest: updateDeck_ModifyFlashcardsRequest = {
                updateData: {},
                flashCardsIdsToAdd: selectedFlashcards,
                flashCardsIdsToRemove: [],
                deckId: selectedDeckId
            };

            const response = await FlashcardController.updateDeck(updateRequest);
            
            if (response) {
                // Success - reset selections and close modal
                setSelectedFlashcards([]);
                setSelectedDeckId('');
                setShowAssignModal(false);
                onFlashcardsCreated(); // Refresh decks in parent
                // Show success message (you can add toast here if needed)
                console.log('Flashcards assigned to deck successfully');
            } else {
                throw new Error('Falha ao atribuir flashcards ao deck');
            }
        } catch (err) {
            console.error('Error assigning flashcards to deck:', err);
            setError('Erro ao atribuir flashcards ao deck');
        } finally {
            setAssignLoading(false);
        }
    };


    return (
        <div className="space-y-8">
            {/* Header */}
            <motion.div
                className="bg-blue-900/20 rounded-xl backdrop-blur-lg border border-blue-500/30 p-4 sm:p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
            >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 lg:gap-6">
                    <div className="flex items-center gap-3 sm:gap-4">
                        <div className="relative">
                            <BookOpen className="w-6 h-6 sm:w-8 sm:h-8 text-green-400" />
                            <div className="absolute -inset-1 bg-green-400/20 rounded-full blur-sm animate-pulse" />
                        </div>
                        <div>
                            <h2 className="text-xl sm:text-2xl font-bold text-green-400">
                                Meus Flashcards
                            </h2>
                            <p className="text-sm sm:text-base text-blue-300">
                                Gerencie seus flashcards criados
                            </p>
                        </div>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                        {selectedFlashcards.length > 0 && (
                            <motion.button
                                className="flex items-center justify-center gap-2 px-3 sm:px-4 py-2 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-200 text-sm sm:text-base"
                                onClick={() => setShowDeleteModal(true)}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.8 }}
                            >
                                <Trash2 className="w-4 h-4" />
                                <span className="hidden sm:inline">Excluir ({selectedFlashcards.length})</span>
                                <span className="sm:hidden">Excluir</span>
                            </motion.button>
                        )}
                        <motion.button
                            className="flex items-center justify-center gap-2 px-3 sm:px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-200 text-sm sm:text-base disabled:opacity-50 disabled:cursor-not-allowed"
                            onClick={() => setShowAssignModal(true)}
                            disabled={flashcards.length === 0}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <FolderPlus className="w-4 h-4" />
                            <span className="hidden sm:inline">Atribuir a Deck</span>
                            <span className="sm:hidden">Atribuir</span>
                        </motion.button>
                        <motion.button
                            className="flex items-center justify-center gap-2 px-3 sm:px-4 py-2 bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white rounded-lg font-semibold transition-all duration-200 text-sm sm:text-base"
                            onClick={() => setShowCreateModal(true)}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <Plus className="w-4 h-4" />
                            <span className="hidden sm:inline">Criar Flashcard</span>
                            <span className="sm:hidden">Criar</span>
                        </motion.button>
                    </div>
                </div>
            </motion.div>

            {/* Filters Section */}
            <motion.div
                className="bg-blue-900/20 rounded-xl backdrop-blur-lg border border-blue-500/30 p-4 sm:p-6 relative"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                style={{ zIndex: 10 }}
            >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="flex items-center gap-3">
                        <div className="relative">
                            <Filter className="w-5 h-5 sm:w-6 sm:h-6 text-blue-400" />
                        </div>
                        <div>
                            <h3 className="text-lg sm:text-xl font-bold text-blue-400">
                                Filtros
                            </h3>
                            <p className="text-sm text-blue-300">
                                {filteredFlashcards.length} de {flashcards.length} flashcards
                            </p>
                        </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                        {hasActiveFilters && (
                            <motion.button
                                className="flex items-center gap-1 px-3 py-1 bg-red-600/20 hover:bg-red-600/30 text-red-400 rounded-lg transition-all duration-200 text-sm"
                                onClick={clearFilters}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <XCircle className="w-4 h-4" />
                                Limpar Filtros
                            </motion.button>
                        )}
                        <motion.button
                            className="flex items-center gap-2 px-3 py-2 bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 rounded-lg transition-all duration-200 text-sm"
                            onClick={() => setShowFilters(!showFilters)}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <Filter className="w-4 h-4" />
                            {showFilters ? 'Ocultar' : 'Mostrar'} Filtros
                        </motion.button>
                    </div>
                </div>

                {/* Filter Controls */}
                {showFilters && (
                    <motion.div
                        className="mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 relative"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        style={{ zIndex: 1 }}
                    >
                        {/* Content Search */}
                        <div>
                            <label className="block text-sm font-medium text-blue-300 mb-2">
                                Buscar Conteúdo
                            </label>
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-400" />
                                <input
                                    type="text"
                                    value={searchContent}
                                    onChange={(e) => setSearchContent(e.target.value)}
                                    placeholder="Pergunta ou resposta..."
                                    className="w-full bg-blue-900/30 border border-blue-500/30 rounded-lg pl-10 pr-4 py-2 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                />
                            </div>
                        </div>

                        {/* Tag Filter */}
                        <div className="relative">
                            <CustomSelect
                                options={tagOptions}
                                value={selectedTag}
                                onChange={(value) => {
                                    console.log('Tag changed to:', value);
                                    setSelectedTag(value as string);
                                }}
                                placeholder="Todas as tags"
                                label="Tag"
                                className="z-[100]"
                            />
                        </div>

                        {/* Difficulty Filter */}
                        <div className="relative">
                            <CustomSelect
                                options={difficultyOptions}
                                value={selectedDifficulty}
                                onChange={(value) => {
                                    console.log('Difficulty changed to:', value);
                                    setSelectedDifficulty(value as number | '');
                                }}
                                placeholder="Todas as dificuldades"
                                label="Dificuldade"
                                className="z-[100]"
                            />
                        </div>
                    </motion.div>
                )}
            </motion.div>

            {/* Content */}
            {loading ? (
                <motion.div
                    className="flex items-center justify-center py-12 sm:py-16"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                >
                    <div className="text-center">
                        <Loader2 className="w-6 h-6 sm:w-8 sm:h-8 animate-spin text-blue-400 mx-auto mb-3 sm:mb-4" />
                        <p className="text-blue-300 text-sm sm:text-base">Carregando flashcards...</p>
                    </div>
                </motion.div>
            ) : error ? (
                <motion.div
                    className="bg-red-900/20 rounded-xl backdrop-blur-lg border border-red-500/30 p-6 sm:p-8 text-center"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                >
                    <AlertCircle className="w-10 h-10 sm:w-12 sm:h-12 text-red-400 mx-auto mb-3 sm:mb-4" />
                    <h3 className="text-lg sm:text-xl font-bold text-red-400 mb-2">Erro ao Carregar</h3>
                    <p className="text-red-300 mb-4 text-sm sm:text-base">{error}</p>
                    <motion.button
                        className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-all duration-200 text-sm sm:text-base"
                        onClick={loadFlashcards}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                    >
                        Tentar Novamente
                    </motion.button>
                </motion.div>
            ) : filteredFlashcards.length === 0 ? (
                <motion.div
                    className="bg-blue-900/20 rounded-xl backdrop-blur-lg border border-blue-500/30 p-6 sm:p-8 text-center"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                >
                    <Brain className="w-12 h-12 sm:w-16 sm:h-16 text-blue-400 mx-auto mb-3 sm:mb-4" />
                    <h3 className="text-lg sm:text-xl font-bold text-blue-300 mb-2">
                        {hasActiveFilters ? 'Nenhum Flashcard Encontrado' : 'Nenhum Flashcard Encontrado'}
                    </h3>
                    <p className="text-blue-200 mb-4 sm:mb-6 text-sm sm:text-base">
                        {hasActiveFilters 
                            ? 'Nenhum flashcard corresponde aos filtros aplicados. Tente ajustar os filtros ou limpar todos os filtros.'
                            : 'Você ainda não criou nenhum flashcard. Crie seu primeiro flashcard para começar!'
                        }
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                        {hasActiveFilters && (
                            <motion.button
                                className="px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-200 text-sm sm:text-base"
                                onClick={clearFilters}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                Limpar Filtros
                            </motion.button>
                        )}
                        {!hasActiveFilters && (
                            <motion.button
                                className="px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white rounded-lg font-semibold transition-all duration-200 text-sm sm:text-base"
                                onClick={() => setShowCreateModal(true)}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                Criar Primeiro Flashcard
                            </motion.button>
                        )}
                    </div>
                </motion.div>
            ) : (
                <motion.div
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ staggerChildren: 0.1 }}
                >
                    {filteredFlashcards.map((flashcard, index) => (
                        <FlashcardCardComponent
                            key={flashcard.id || index}
                            flashcard={flashcard}
                            index={index}
                            isSelected={selectedFlashcards.includes(flashcard.id || '')}
                            onToggleSelection={toggleFlashcardSelection}
                            onEdit={handleEditFlashcard}
                        />
                    ))}
                </motion.div>
            )}

            {/* Create Flashcard Modal */}
            {showCreateModal && (
                <CreateFlashcardModal
                    onFlashcardCreated={handleFlashcardCreated}
                    onClose={() => setShowCreateModal(false)}
                />
            )}

            {/* Edit Flashcard Modal */}
            {showEditModal && editingFlashcard && (
                <EditFlashcardModal
                    flashcard={editingFlashcard}
                    onFlashcardUpdated={handleFlashcardUpdated}
                    onClose={() => {
                        setShowEditModal(false);
                        setEditingFlashcard(null);
                    }}
                />
            )}

            {/* Delete Confirmation Modal */}
            {showDeleteModal && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                    onClick={() => setShowDeleteModal(false)}
                >
                    <motion.div
                        initial={{ scale: 0.95, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.95, opacity: 0 }}
                        className="bg-[#0F2057] rounded-xl p-4 sm:p-6 max-w-md w-full max-h-[90vh] overflow-y-auto border border-red-500/30"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <div className="flex justify-between items-start sm:items-center mb-4 sm:mb-6 gap-2">
                            <h2 className="text-lg sm:text-xl font-bold text-red-400 flex items-center gap-2">
                                <Trash2 className="w-5 h-5 sm:w-6 sm:h-6 flex-shrink-0" />
                                <span className="leading-tight">Confirmar Exclusão</span>
                            </h2>
                            <button
                                onClick={() => setShowDeleteModal(false)}
                                className="text-blue-300 hover:text-blue-100 transition-all rounded-full p-1 sm:p-2 hover:bg-blue-500/30 flex-shrink-0"
                            >
                                <X className="w-4 h-4 sm:w-5 sm:h-5" />
                            </button>
                        </div>

                        <div className="space-y-4">
                            <div className="text-center">
                                <div className="w-16 h-16 mx-auto mb-4 bg-red-500/20 rounded-full flex items-center justify-center">
                                    <Trash2 className="w-8 h-8 text-red-400" />
                                </div>
                                <h3 className="text-lg font-semibold text-red-300 mb-2">
                                    Excluir {selectedFlashcards.length} Flashcard{selectedFlashcards.length > 1 ? 's' : ''}?
                                </h3>
                                <p className="text-blue-200 text-sm sm:text-base">
                                    Esta ação não pode ser desfeita. Os flashcards selecionados serão permanentemente removidos.
                                </p>
                            </div>

                            <div className="bg-blue-900/20 rounded-lg p-3 max-h-32 overflow-y-auto">
                                <p className="text-blue-300 text-sm font-medium mb-2">Flashcards a serem excluídos:</p>
                                <div className="space-y-1">
                                    {selectedFlashcards.map((flashcardId) => {
                                        const flashcard = flashcards.find(f => f.id === flashcardId);
                                        return flashcard ? (
                                            <div key={flashcardId} className="text-blue-200 text-xs sm:text-sm">
                                                • {flashcard.question.substring(0, 50)}...
                                            </div>
                                        ) : null;
                                    })}
                                </div>
                            </div>

                            {error && (
                                <div className="flex items-center gap-2 text-red-400 text-xs sm:text-sm bg-red-500/10 p-2 sm:p-3 rounded-lg">
                                    <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                                    <span className="break-words">{error}</span>
                                </div>
                            )}

                            <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3 pt-4 border-t border-blue-500/20">
                                <button
                                    onClick={() => setShowDeleteModal(false)}
                                    className="px-3 sm:px-4 py-2 border border-blue-500/30 text-blue-300 hover:bg-blue-900/30 rounded-lg transition-all text-sm sm:text-base"
                                >
                                    Cancelar
                                </button>
                                <button
                                    onClick={handleDeleteFlashcards}
                                    disabled={deleteLoading}
                                    className="px-3 sm:px-4 py-2 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base flex items-center justify-center gap-2"
                                >
                                    {deleteLoading ? (
                                        <>
                                            <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin" />
                                            <span>Excluindo...</span>
                                        </>
                                    ) : (
                                        <>
                                            <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                                            <span>Excluir</span>
                                        </>
                                    )}
                                </button>
                            </div>
                        </div>
                    </motion.div>
                </motion.div>
            )}

            {/* Assign to Deck Modal */}
            {showAssignModal && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                    onClick={() => setShowAssignModal(false)}
                >
                    <motion.div
                        initial={{ scale: 0.95, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.95, opacity: 0 }}
                        className="bg-[#0F2057] rounded-xl p-4 sm:p-6 max-w-md w-full max-h-[90vh] overflow-y-auto border border-blue-500/30"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <div className="flex justify-between items-start sm:items-center mb-4 sm:mb-6 gap-2">
                            <h2 className="text-lg sm:text-xl font-bold text-purple-400 flex items-center gap-2">
                                <FolderPlus className="w-5 h-5 sm:w-6 sm:h-6 flex-shrink-0" />
                                <span className="leading-tight">Atribuir Flashcards a Deck</span>
                            </h2>
                            <button
                                onClick={() => setShowAssignModal(false)}
                                className="text-blue-300 hover:text-blue-100 transition-all rounded-full p-1 sm:p-2 hover:bg-blue-500/30 flex-shrink-0"
                            >
                                <X className="w-4 h-4 sm:w-5 sm:h-5" />
                            </button>
                        </div>

                        <div className="space-y-4">
                            <div>
                                <CustomSelect
                                    options={[
                                        { value: '', label: 'Escolha um deck...' },
                                        ...decks.map((deck) => ({
                                            value: deck.apiId,
                                            label: deck.titulo
                                        }))
                                    ]}
                                    value={selectedDeckId}
                                    onChange={(value) => setSelectedDeckId(value as string)}
                                    placeholder="Escolha um deck..."
                                    className="z-50"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-blue-300 mb-2">
                                    Flashcards Selecionados ({selectedFlashcards.length})
                                </label>
                                <div className="bg-blue-900/20 rounded-lg p-3 max-h-24 sm:max-h-32 overflow-y-auto">
                                    {selectedFlashcards.length === 0 ? (
                                        <p className="text-blue-400 text-xs sm:text-sm">Nenhum flashcard selecionado</p>
                                    ) : (
                                        <div className="space-y-1">
                                            {selectedFlashcards.map((flashcardId) => {
                                                const flashcard = flashcards.find(f => f.id === flashcardId);
                                                return flashcard ? (
                                                    <div key={flashcardId} className="text-blue-200 text-xs sm:text-sm">
                                                        • {flashcard.question.substring(0, 40)}...
                                                    </div>
                                                ) : null;
                                            })}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {error && (
                                <div className="flex items-center gap-2 text-red-400 text-xs sm:text-sm bg-red-500/10 p-2 sm:p-3 rounded-lg">
                                    <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                                    <span className="break-words">{error}</span>
                                </div>
                            )}

                            <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3 pt-4 border-t border-blue-500/20">
                                <button
                                    onClick={() => setShowAssignModal(false)}
                                    className="px-3 sm:px-4 py-2 border border-blue-500/30 text-blue-300 hover:bg-blue-900/30 rounded-lg transition-all text-sm sm:text-base"
                                >
                                    Cancelar
                                </button>
                                <button
                                    onClick={handleAssignToDeck}
                                    disabled={assignLoading || !selectedDeckId || selectedFlashcards.length === 0}
                                    className="px-3 sm:px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base flex items-center justify-center gap-2"
                                >
                                    {assignLoading ? (
                                        <>
                                            <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin" />
                                            <span>Atribuindo...</span>
                                        </>
                                    ) : (
                                        'Atribuir ao Deck'
                                    )}
                                </button>
                            </div>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </div>
    );
}
