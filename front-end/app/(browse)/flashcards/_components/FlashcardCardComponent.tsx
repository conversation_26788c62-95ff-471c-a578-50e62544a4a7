"use client"

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON>, Edit3 } from 'lucide-react';
import { translateContent } from '@/utils/translates';
interface FlashcardCardComponentProps {
    flashcard: {
        id?: string;
        question: string;
        answer: string;
        difficulty: number;
        tags?: string[];
        isFromAi?: boolean;
    };
    index: number;
    isSelected: boolean;
    onToggleSelection: (id: string) => void;
    onEdit?: (flashcard: any) => void;
}

const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
        case 1:
            return 'bg-green-500/20 text-green-400 border border-green-500/30';
        case 2:
            return 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30';
        case 3:
            return 'bg-red-500/20 text-red-400 border border-red-500/30';
        default:
            return 'bg-blue-500/20 text-blue-400 border border-blue-500/30';
    }
};

const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
        case 1:
            return 'Fácil';
        case 2:
            return 'Médio';
        case 3:
            return 'Difícil';
        default:
            return 'N/A';
    }
};

export function FlashcardCardComponent({
    flashcard,
    index,
    isSelected,
    onToggleSelection,
    onEdit
}: FlashcardCardComponentProps) {
    return (
        <motion.div
            className={`bg-blue-900/20 rounded-xl backdrop-blur-lg border p-6 transition-all duration-200 cursor-pointer ${
                isSelected 
                    ? 'border-purple-500/50 bg-purple-900/20' 
                    : 'border-blue-500/30 hover:border-green-500/50'
            }`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
            onClick={() => onToggleSelection(flashcard.id || '')}
        >
            <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-2">
                    <Brain className="w-5 h-5 text-green-400" />
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getDifficultyColor(flashcard.difficulty)}`}>
                        {getDifficultyText(flashcard.difficulty)}
                    </span>
                </div>
                <div className="flex items-center gap-2">
                    {onEdit && (
                        <motion.button
                            className="p-1 text-blue-400 hover:text-blue-300 hover:bg-blue-800/30 rounded transition-all duration-200"
                            onClick={(e) => {
                                e.stopPropagation();
                                onEdit(flashcard);
                            }}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                        >
                            <Edit3 className="w-4 h-4" />
                        </motion.button>
                    )}
                    {flashcard.isFromAi && (
                        <div className="flex items-center gap-1 text-purple-400">
                            <Star className="w-4 h-4" />
                            <span className="text-xs">IA</span>
                        </div>
                    )}
                    {isSelected && (
                        <div className="w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center">
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                    )}
                </div>
            </div>
            
            <div className="space-y-3">
                <div>
                    <h4 className="text-sm font-semibold text-blue-300 mb-1">Pergunta:</h4>
                    <p className="text-blue-100 text-sm line-clamp-2">{flashcard.question}</p>
                </div>
                
                <div>
                    <h4 className="text-sm font-semibold text-blue-300 mb-1">Resposta:</h4>
                    <p className="text-blue-100 text-sm line-clamp-2">{flashcard.answer}</p>
                </div>
            </div>

            {flashcard.tags && flashcard.tags.length > 0 && (
                <div className="mt-4 pt-4 border-t border-blue-500/20">
                    <div className="flex flex-wrap gap-1">
                        {flashcard.tags.slice(0, 3).map((tag, tagIndex) => (
                            <span
                                key={tagIndex}
                                className="px-2 py-1 bg-blue-800/30 text-blue-300 text-xs rounded-full"
                            >
                                {translateContent(tag)}
                            </span>
                        ))}
                        {flashcard.tags.length > 3 && (
                            <span className="px-2 py-1 bg-blue-800/30 text-blue-300 text-xs rounded-full">
                                +{flashcard.tags.length - 3}
                            </span>
                        )}
                    </div>
                </div>
            )}
        </motion.div>
    );
}
