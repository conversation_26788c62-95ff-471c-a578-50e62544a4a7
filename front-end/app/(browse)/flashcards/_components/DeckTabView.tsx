"use client"

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { BookOpen, Loader2 } from 'lucide-react';
import { Deck, Flashcard, mapAPIDeckToUI } from '../utils/dataMapping';
import { FlashCard } from './FlashCard';
import { DeckProgress } from './DeckProgress';
import { DeckComplete } from './DeckComplete';
import { DeckCardComponent } from './DeckCardComponent';
import FlashcardController from '@/services/CDS/api/FlashcardController';
import { FlashcardStatistics } from '../../../../services/CDS/interfaces';

interface DeckTabViewProps {
    decks: Deck[];
    deckAtual: Deck | null;
    cardAtual: number;
    mostrandoVerso: boolean;
    conhecidos: number[];
    revisao: number[];
    deckCompleto: boolean;
    escolhendoDeck: boolean;
    originalDeckData?: any; // Raw API data with statistics
    cardStatistics?: { [cardApiId: string]: FlashcardStatistics };
    onHandleChangeDeck: (deck: Deck) => void;
    onHandleVirarCard: () => void;
    onHandleAcertei: () => void;
    onHandleErrei: () => void;
    onReiniciarDeck: () => void;
    onSetEscolhendoDeck: (escolhendo: boolean) => void;
    onDeckDataFetched?: (originalData: any, statistics: { [cardApiId: string]: FlashcardStatistics }) => void;
    submittingAnswers?: boolean;
    submitError?: string | null;
}

// Component for empty decks
function EmptyDeckMessage({ deck }: { deck: Deck }) {
    return (
        <motion.div
            className="flex flex-col items-center justify-center py-16 px-8 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
        >
            <div className="bg-blue-900/20 rounded-xl backdrop-blur-lg border border-blue-500/30 p-8 max-w-md">
                <BookOpen className="w-16 h-16 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-blue-300 mb-2">
                    Deck Vazio
                </h3>
                <p className="text-blue-200 mb-4">
                    O deck {deck.titulo} não possui flashcards ainda.
                </p>
                <p className="text-sm text-blue-400 mb-6">
                    Este deck foi criado mas ainda não tem conteúdo. Tente criar um novo deck com IA.
                </p>
            </div>
        </motion.div>
    );
}

function StartingDeckMessage() {
    return (
        <motion.div
            className="flex flex-col items-center justify-center py-16 px-8 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
        >
            <div className="bg-blue-900/20 rounded-xl backdrop-blur-lg border border-blue-500/30 p-8 max-w-md">
                <BookOpen className="w-16 h-16 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-blue-300 mb-2">
                    Selecione um deck
                </h3>
                <p className="text-blue-200 mb-4">
                    Selecione um deck na barra lateral para começar a estudar.
                </p>
            </div>
        </motion.div>
    );
}

// Component for no decks available
function NoDecksAvailable() {
    return (
        <motion.div
            className="flex flex-col items-center justify-center py-16 px-8 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
        >
            <div className="bg-blue-900/20 rounded-xl backdrop-blur-lg border border-blue-500/30 p-8 max-w-md">
                <BookOpen className="w-16 h-16 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-blue-300 mb-2">
                    Nenhum Deck Disponível
                </h3>
                <p className="text-blue-200 mb-4">
                    Você ainda não possui nenhum deck de flashcards.
                </p>
                <p className="text-sm text-blue-400 mb-6">
                    Crie seu primeiro deck para começar a estudar!
                </p>
            </div>
        </motion.div>
    );
}

// Component for choosing a deck
function EscolherDeck() {
    return (
        <motion.div
            className="flex flex-col items-center justify-center py-16 px-8 text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
        >
            <div className="bg-blue-900/20 rounded-xl backdrop-blur-lg border border-blue-500/30 p-8 max-w-md">
                <BookOpen className="w-16 h-16 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-purple-400 mb-2">
                    Escolha um Deck
                </h3>
                <p className="text-blue-200 mb-4">
                    Selecione um deck na barra lateral para começar a estudar.
                </p>
                <p className="text-sm text-blue-400">
                    Use os filtros para encontrar o deck que deseja estudar.
                </p>
            </div>
        </motion.div>
    );
}

export function DeckTabView({
    decks,
    deckAtual,
    cardAtual,
    mostrandoVerso,
    conhecidos,
    revisao,
    deckCompleto,
    escolhendoDeck,
    originalDeckData,
    cardStatistics,
    onHandleChangeDeck,
    onHandleVirarCard,
    onHandleAcertei,
    onHandleErrei,
    onReiniciarDeck,
    onSetEscolhendoDeck,
    onDeckDataFetched,
    submittingAnswers = false,
    submitError = null
}: DeckTabViewProps) {
    const [loadingDeck, setLoadingDeck] = useState<string | null>(null);
    const [fetchedDecks, setFetchedDecks] = useState<Map<string, Deck>>(new Map())

    const handleDeckClick = async (deck: Deck) => {
        console.log('handleDeckClick', deck);
        if (!deck.apiId) {
            console.error('Deck API ID not found');
            return;
        }

        // Check if we already have the full deck data locally
        if (fetchedDecks.has(deck.apiId)) {
            const cachedDeck = fetchedDecks.get(deck.apiId);
            if (cachedDeck) {
                onHandleChangeDeck(cachedDeck);
                return;
            }
        }

        setLoadingDeck(deck.apiId);
        
        try {
            // Fetch the full deck data with flashcards
            const fullDeckData = await FlashcardController.getDeckById(deck.apiId);
            
            if (fullDeckData) {
                console.log('Full deck data from API:', fullDeckData);
                
                // Map the API response to UI format
                const mappedDeck = mapAPIDeckToUI(fullDeckData, deck.id - 1);
                console.log('Mapped deck:', mappedDeck);
                console.log('Mapped deck cards:', mappedDeck.cards);
                
                // Store the fetched deck data locally
                setFetchedDecks(prev => new Map(prev).set(deck.apiId!, mappedDeck));
                
                // Extract statistics from the original API data
                const statistics: { [cardApiId: string]: FlashcardStatistics } = {};
                if (fullDeckData.flashcards) {
                    fullDeckData.flashcards.forEach((deckFlashcard: any) => {
                        if (deckFlashcard.flashcard?.statistics?.flashcardId) {
                            statistics[deckFlashcard.flashcard.statistics.flashcardId] = deckFlashcard.flashcard.statistics;
                        }
                    });
                }
                
                // Call the callback to pass the original data and statistics to parent
                if (onDeckDataFetched) {
                    onDeckDataFetched(fullDeckData, statistics);
                }
                
                onHandleChangeDeck(mappedDeck);
            } else {
                console.error('Failed to fetch deck data');
            }
        } catch (error) {
            console.error('Error fetching deck:', error);
        } finally {
            setLoadingDeck(null);
        }
    };
    return (
        <div className="lg:grid lg:grid-cols-[1fr,380px] gap-8">
            {/* Área principal do flashcard */}
            <div className="space-y-8">
                {decks.length === 0 ? (
                    <NoDecksAvailable />
                ) : escolhendoDeck ? (
                    <EscolherDeck />
                ) : deckCompleto ? (
                    <DeckComplete 
                        conhecidos={conhecidos}
                        revisao={revisao}
                        deckData={deckAtual || undefined}
                        cardStatistics={cardStatistics}
                        onReiniciar={onReiniciarDeck}
                        onEscolherOutroDeck={() => onSetEscolhendoDeck(true)}
                        submittingAnswers={submittingAnswers}
                        submitError={submitError}
                    />
                ) : deckAtual && deckAtual.cards.length > 0 && deckAtual.cards[cardAtual] ? (
                    <>
                        <FlashCard
                            card={deckAtual.cards[cardAtual]}
                            mostrandoVerso={mostrandoVerso}
                            onVirar={onHandleVirarCard}
                            onAcertei={onHandleAcertei}
                            onErrei={onHandleErrei}
                        />

                        <DeckProgress
                            cardAtual={cardAtual}
                            totalCards={deckAtual.cards.length}
                            conhecidos={conhecidos}
                            revisao={revisao}
                        />
                    </>
                ) : deckAtual && deckAtual.cards.length === 0 ? (
                    <EmptyDeckMessage deck={deckAtual} />
                ) : deckAtual && deckAtual.cards.length > 0 && !deckAtual.cards[cardAtual] ? (
                    <div className="text-center py-8">
                        <p className="text-gray-400">Card não encontrado. Tente reiniciar o deck.</p>
                    </div>
                ) : (
                    <StartingDeckMessage />
                )}
            </div>

            {/* Coluna lateral direita - Lista de Decks */}
            <div className="mt-8 lg:mt-0">
                <div className="bg-blue-900/20 rounded-xl backdrop-blur-lg border border-blue-500/30 p-6">
                    <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
                        <BookOpen className="w-5 h-5 text-purple-400" />
                        {decks.length === 0 ? 'Criar Deck' : 'Meus Decks'}
                    </h2>

                    {decks.length === 0 ? (
                        <div className="text-center py-8">
                            <p className="text-blue-300 mb-4">
                                Você ainda não possui nenhum deck de flashcards.
                            </p>
                            <motion.button
                                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-200"
                                onClick={() => {
                                    // Scroll to the CreateDeckWithAI component in the header
                                    const headerElement = document.querySelector('header');
                                    if (headerElement) {
                                        headerElement.scrollIntoView({ behavior: 'smooth' });
                                    }
                                }}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                Criar Primeiro Deck
                            </motion.button>
                        </div>
                    ) : (
                        <>
                            {/* Lista de Decks */}
                            <div className="space-y-2">
                                <h3 className="text-lg font-semibold text-purple-400 mb-4 flex items-center gap-2">
                                    Decks Disponíveis
                                    <span className="text-sm text-blue-300">
                                        {decks.length} decks
                                    </span>
                                </h3>
                                {decks.map((deck) => (
                                        <DeckCardComponent
                                            key={deck.id}
                                            deck={deck}
                                            isSelected={deckAtual?.id === deck.id}
                                            isLoading={loadingDeck === deck.apiId}
                                            onClick={handleDeckClick}
                                        />
                                    ))
                                }
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
}
