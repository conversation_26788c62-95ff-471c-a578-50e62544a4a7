"use client"

import React from 'react';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { Deck } from '../utils/dataMapping';

interface DeckCardComponentProps {
    deck: Deck;
    isSelected: boolean;
    isLoading: boolean;
    onClick: (deck: Deck) => void;
}

export function DeckCardComponent({
    deck,
    isSelected,
    isLoading,
    onClick
}: DeckCardComponentProps) {
    return (
        <motion.div
            className={`p-4 rounded-lg transition-all duration-200 
                ${isLoading 
                    ? 'cursor-wait opacity-75' 
                    : 'cursor-pointer'
                }
                ${isSelected 
                    ? 'bg-purple-900/30 border border-purple-500/50' 
                    : 'bg-blue-900/30 border border-blue-500/30 hover:bg-blue-900/50'
                }`}
            onClick={() => !isLoading && onClick(deck)}
            whileHover={isLoading ? {} : { scale: 1.02 }}
            whileTap={isLoading ? {} : { scale: 0.98 }}
        >
            <div className="flex justify-between items-start">
                <h4 className="font-bold mb-1">{deck.titulo}</h4>
                {isLoading && (
                    <Loader2 className="w-4 h-4 animate-spin text-purple-400" />
                )}
            </div>
            <p className="text-sm text-blue-300">{deck.descricao}</p>
            <div className="mt-2 text-sm text-purple-400">
                {isLoading ? 'Carregando...' : deck.cards.length > 0 ? `${deck.cards.length} cards` : ''}
            </div>
        </motion.div>
    );
}
