"use client"

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Edit3, Loader2, <PERSON>, <PERSON>, <PERSON>, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { CustomSelect } from '@/components/ui/CustomSelect'
import { toast } from 'sonner'
import FlashcardController from '@/services/CDS/api/FlashcardController'
import { Flashcard, UpdateFlashcardRequest } from '@/services/CDS/interfaces/flashcard'

interface EditFlashcardModalProps {
  flashcard: Flashcard
  onFlashcardUpdated: () => void
  onClose: () => void
}

export function EditFlashcardModal({ flashcard, onFlashcardUpdated, onClose }: EditFlashcardModalProps) {
  // Form fields
  const [question, setQuestion] = useState(flashcard.question)
  const [answer, setAnswer] = useState(flashcard.answer)
  const [tags, setTags] = useState<string[]>(flashcard.tags || [])
  const [newTag, setNewTag] = useState('')
  const [difficulty, setDifficulty] = useState<1 | 2 | 3>(flashcard.difficulty)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  // Reset form when flashcard changes
  useEffect(() => {
    setQuestion(flashcard.question)
    setAnswer(flashcard.answer)
    setTags(flashcard.tags || [])
    setDifficulty(flashcard.difficulty)
    setError('')
  }, [flashcard])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!question.trim()) {
      setError('Por favor, informe a pergunta do flashcard')
      return
    }
    
    if (!answer.trim()) {
      setError('Por favor, informe a resposta do flashcard')
      return
    }

    if (!flashcard.id) {
      setError('ID do flashcard não encontrado')
      return
    }
    
    setIsLoading(true)
    setError('')

    try {
      const updateRequest: UpdateFlashcardRequest = {
        flashcardId: flashcard.id,
        updateData: {
          question: question.trim(),
          answer: answer.trim(),
          tags: tags.length > 0 ? tags : undefined,
          difficulty: difficulty
        }
      }

      const response = await FlashcardController.updateFlashcard(updateRequest)
      
      if (!response) {
        throw new Error('Falha ao atualizar flashcard')
      }

      toast.success('Flashcard atualizado com sucesso!')
      onFlashcardUpdated()
      onClose()
      
    } catch (error) {
      console.error('Erro ao atualizar flashcard:', error)
      const message = error instanceof Error ? error.message : 'Erro ao atualizar flashcard'
      setError(message)
      toast.error(message)
    } finally {
      setIsLoading(false)
    }
  }

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  // Options for CustomSelect components
  const difficultyOptions = [
    { value: 1, label: 'Fácil' },
    { value: 2, label: 'Médio' },
    { value: 3, label: 'Difícil' }
  ]

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-[#0F2057] rounded-xl p-6 max-w-2xl w-full border border-blue-500/30 max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex justify-between items-center mb-6 bg-blue-900/30 p-4 rounded-xl border border-blue-400/20 shadow-[0_0_15px_rgba(59,130,246,0.2)]">
            <h2 className="text-xl font-bold flex items-center gap-3">
              <div className="relative">
                <Edit3 className="w-6 h-6 text-blue-400 animate-pulse" />
                <div className="absolute -inset-1 bg-blue-500/20 rounded-full blur-sm animate-pulse" />
              </div>
              <span className="bg-gradient-to-r from-blue-400 via-green-400 to-blue-400 bg-clip-text text-transparent">
                Editar Flashcard
              </span>
            </h2>
            <button
              onClick={onClose}
              className="text-blue-300 hover:text-blue-100 transition-all rounded-full p-2 hover:bg-blue-500/30 hover:shadow-[0_0_10px_rgba(59,130,246,0.3)] border border-transparent hover:border-blue-400/40"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Question */}
            <div>
              <label className="block text-sm font-medium text-blue-300 mb-2">
                Pergunta *
              </label>
              <textarea
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                className="w-full bg-blue-900/30 border border-blue-500/30 rounded-lg px-4 py-3 text-blue-100 
                  placeholder:text-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                placeholder="Digite a pergunta do flashcard..."
                rows={3}
                required
              />
            </div>

            {/* Answer */}
            <div>
              <label className="block text-sm font-medium text-blue-300 mb-2">
                Resposta *
              </label>
              <textarea
                value={answer}
                onChange={(e) => setAnswer(e.target.value)}
                className="w-full bg-blue-900/30 border border-blue-500/30 rounded-lg px-4 py-3 text-blue-100 
                  placeholder:text-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                placeholder="Digite a resposta do flashcard..."
                rows={3}
                required
              />
            </div>

            {/* Difficulty */}
            <CustomSelect
              options={difficultyOptions}
              value={difficulty}
              onChange={(value) => setDifficulty(value as 1 | 2 | 3)}
              label="Dificuldade *"
              placeholder="Selecionar dificuldade"
              className="z-[100]"
            />

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-blue-300 mb-2">
                Tags (opcional)
              </label>
              <div className="space-y-3">
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="flex-1 bg-blue-900/30 border border-blue-500/30 rounded-lg px-4 py-2 text-blue-100 
                      placeholder:text-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Digite uma tag e pressione Enter..."
                  />
                  <Button
                    type="button"
                    onClick={addTag}
                    disabled={!newTag.trim()}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                  >
                    <Tag className="w-4 h-4" />
                  </Button>
                </div>
                
                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag, index) => (
                      <motion.span
                        key={index}
                        className="flex items-center gap-2 px-3 py-1 bg-blue-800/30 text-blue-300 rounded-full text-sm"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                      >
                        <Tag className="w-3 h-3" />
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="text-blue-400 hover:text-red-400 transition-colors"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </motion.span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="flex items-center gap-2 text-red-400 text-sm bg-red-500/10 p-3 rounded-lg">
                <AlertCircle className="w-4 h-4" />
                {error}
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t border-blue-500/20">
              <Button
                type="button"
                onClick={onClose}
                variant="outline"
                className="border-blue-500/30 text-blue-300 hover:bg-blue-900/30"
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !question.trim() || !answer.trim()}
                className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Atualizando...
                  </>
                ) : (
                  <>
                    <Edit3 className="w-4 h-4 mr-2" />
                    Atualizar Flashcard
                  </>
                )}
              </Button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
