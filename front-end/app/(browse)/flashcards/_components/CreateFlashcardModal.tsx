"use client"

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Plus, Loader2, X, BookOpen, Tag, Brain, AlertCircle, Hash, Wand2, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { CustomSelect } from '@/components/ui/CustomSelect'
import { toast } from 'sonner'
import FlashcardController from '@/services/CDS/api/FlashcardController'
import { useAuth } from '@/context/AuthContext'
import { Deck } from '../utils/dataMapping'
import { CreateFlashcardRequest } from '@/services/CDS/interfaces'

interface CreateFlashcardModalProps {
  onFlashcardCreated: () => void
  onClose: () => void
}

export function CreateFlashcardModal({ onFlashcardCreated, onClose }: CreateFlashcardModalProps) {
  // Creation mode: 'manual' or 'ai'
  const [creationMode, setCreationMode] = useState<'manual' | 'ai'>('manual')
  
  // Manual creation fields
  const [question, setQuestion] = useState('')
  const [answer, setAnswer] = useState('')
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState('')
  
  // AI creation fields
  const [topic, setTopic] = useState('')
  const [numberOfCards, setNumberOfCards] = useState(3)
  const [aiDifficulty, setAiDifficulty] = useState<'fácil' | 'médio' | 'difícil'>('médio')
  
  // Common fields
  const [difficulty, setDifficulty] = useState<1 | 2 | 3>(1)
  const [numberOfFlashcards, setNumberOfFlashcards] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const { authData } = useAuth()

  // Load current flashcard count when component mounts
  useEffect(() => {
    const loadFlashcardCount = async () => {
      try {
        const response = await FlashcardController.getSelfFlashcards(1, 1) // Just get count
        if (response && response.data) {
          // Use the total count of user's flashcards
          setNumberOfFlashcards(response.data.length + 1)
        }
      } catch (error) {
        console.warn('Could not load flashcard count:', error)
        // Keep default value of 1
      }
    }

    loadFlashcardCount()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation based on creation mode
    if (creationMode === 'manual') {
      if (!question.trim()) {
        setError('Por favor, informe a pergunta do flashcard')
        return
      }
      
      if (!answer.trim()) {
        setError('Por favor, informe a resposta do flashcard')
        return
      }
    } else {
      if (!topic.trim()) {
        setError('Por favor, informe o tópico para os flashcards')
        return
      }
      
      if (numberOfCards < 1 || numberOfCards > 5) {
        setError('Número de flashcards deve ser entre 1 e 5')
        return
      }
    }

    if (!authData?._id) {
      setError('Usuário não autenticado')
      return
    }
    
    setIsLoading(true)
    setError('')

    try {
      if (creationMode === 'manual') {
        // Manual flashcard creation
        const flashcardRequest: CreateFlashcardRequest = {
          flashCardData: {
            question: question.trim(),
            answer: answer.trim(),
            tags: tags.length > 0 ? tags : undefined,
            difficulty: difficulty,
            ownerId: authData._id
          },
          isFromAi: false,
          numberOfFlashcards: 1
        }

        const response = await FlashcardController.createFlashcard(flashcardRequest)
        
        if (!response) {
          throw new Error('Falha ao criar flashcard')
        }

        toast.success('Flashcard criado com sucesso!')
        onFlashcardCreated()
        
        // Reset manual form
        setQuestion('')
        setAnswer('')
        setDifficulty(1)
        setTags([])
        setNewTag('')
        setNumberOfFlashcards(prev => prev + 1) // Increment for next flashcard
        
      } else {
        // AI flashcard creation - create multiple flashcards with AI-generated content
        const createdFlashcards = []
        
        for (let i = 0; i < numberOfCards; i++) {
          // Generate AI content based on topic and difficulty
          const aiQuestion = `Pergunta ${i + 1} sobre ${topic} (${aiDifficulty})`
          const aiAnswer = `Resposta ${i + 1} sobre ${topic} (${aiDifficulty})`
          
          const flashcardRequest = {
            flashCardData: {
              question: aiQuestion,
              answer: aiAnswer,
              tags: tags.length > 0 ? tags : [],
              difficulty: (aiDifficulty === 'fácil' ? 1 : aiDifficulty === 'médio' ? 2 : 3) as 1 | 2 | 3,
              ownerId: authData._id
            },
            isFromAi: true,
            numberOfFlashcards: 1
          }

          const response = await FlashcardController.createFlashcard(flashcardRequest)
          
          if (!response) {
            throw new Error(`Falha ao criar flashcard ${i + 1}`)
          }
          
          createdFlashcards.push(response)
        }

        toast.success(`${numberOfCards} flashcards criados com IA com sucesso!`)
        onFlashcardCreated()
        
        // Reset AI form
        setTopic('')
        setNumberOfCards(3)
        setAiDifficulty('médio')
        setTags([])
        setNewTag('')
        setNumberOfFlashcards(prev => prev + numberOfCards) // Increment by number of cards created
      }
      
    } catch (error) {
      console.error('Erro ao criar flashcard:', error)
      const message = error instanceof Error ? error.message : 'Erro ao criar flashcard'
      setError(message)
      toast.error(message)
    } finally {
      setIsLoading(false)
    }
  }

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  const getDifficultyColor = (level: 1 | 2 | 3) => {
    switch (level) {
      case 1: return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 2: return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 3: return 'bg-red-500/20 text-red-400 border-red-500/30'
      default: return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
    }
  }

  const getDifficultyText = (level: 1 | 2 | 3) => {
    switch (level) {
      case 1: return 'Fácil'
      case 2: return 'Médio'
      case 3: return 'Difícil'
      default: return 'N/A'
    }
  }

  // Options for CustomSelect components
  const difficultyOptions = [
    { value: 1, label: 'Fácil' },
    { value: 2, label: 'Médio' },
    { value: 3, label: 'Difícil' }
  ]

  const aiDifficultyOptions = [
    { value: 'fácil', label: 'Fácil' },
    { value: 'médio', label: 'Médio' },
    { value: 'difícil', label: 'Difícil' }
  ]

  const numberOfCardsOptions = [
    { value: 1, label: '1' },
    { value: 2, label: '2' },
    { value: 3, label: '3' },
    { value: 4, label: '4' },
    { value: 5, label: '5' }
  ]

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-[#0F2057] rounded-xl p-6 max-w-2xl w-full border border-blue-500/30 max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex justify-between items-center mb-6 bg-blue-900/30 p-4 rounded-xl border border-blue-400/20 shadow-[0_0_15px_rgba(59,130,246,0.2)]">
            <h2 className="text-xl font-bold flex items-center gap-3">
              <div className="relative">
                <Brain className="w-6 h-6 text-green-400 animate-pulse" />
                <div className="absolute -inset-1 bg-green-500/20 rounded-full blur-sm animate-pulse" />
              </div>
              <span className="bg-gradient-to-r from-green-400 via-blue-400 to-green-400 bg-clip-text text-transparent">
                Criar Flashcard
              </span>
            </h2>
            <button
              onClick={onClose}
              className="text-blue-300 hover:text-blue-100 transition-all rounded-full p-2 hover:bg-blue-500/30 hover:shadow-[0_0_10px_rgba(59,130,246,0.3)] border border-transparent hover:border-blue-400/40"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Creation Mode Toggle */}
          <div className="mb-6">
            <div className="flex space-x-1 bg-blue-900/20 p-1 rounded-lg border border-blue-500/30">
              <motion.button
                className={`flex-1 px-4 py-3 rounded-md font-semibold transition-all duration-200 flex items-center justify-center gap-2 ${
                  creationMode === 'manual'
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'text-blue-300 hover:text-white hover:bg-blue-800/30'
                }`}
                onClick={() => setCreationMode('manual')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <User className="w-4 h-4" />
                Manual
              </motion.button>
              <motion.button
                className={`flex-1 px-4 py-3 rounded-md font-semibold transition-all duration-200 flex items-center justify-center gap-2 ${
                  creationMode === 'ai'
                    ? 'bg-purple-600 text-white shadow-lg'
                    : 'text-purple-300 hover:text-white hover:bg-purple-800/30'
                }`}
                onClick={() => setCreationMode('ai')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Wand2 className="w-4 h-4" />
                IA
              </motion.button>
            </div>
          </div>
          <form onSubmit={handleSubmit} className="space-y-6">
            {creationMode === 'manual' ? (
              <>
                {/* Manual Creation Fields */}
                {/* Question */}
                <div>
                  <label className="block text-sm font-medium text-blue-300 mb-2">
                    Pergunta *
                  </label>
                  <textarea
                    value={question}
                    onChange={(e) => setQuestion(e.target.value)}
                    className="w-full bg-blue-900/30 border border-blue-500/30 rounded-lg px-4 py-3 text-blue-100 
                      placeholder:text-blue-400 focus:outline-none focus:ring-2 focus:ring-green-500 resize-none"
                    placeholder="Digite a pergunta do flashcard..."
                    rows={3}
                    required
                  />
                </div>

                {/* Answer */}
                <div>
                  <label className="block text-sm font-medium text-blue-300 mb-2">
                    Resposta *
                  </label>
                  <textarea
                    value={answer}
                    onChange={(e) => setAnswer(e.target.value)}
                    className="w-full bg-blue-900/30 border border-blue-500/30 rounded-lg px-4 py-3 text-blue-100 
                      placeholder:text-blue-400 focus:outline-none focus:ring-2 focus:ring-green-500 resize-none"
                    placeholder="Digite a resposta do flashcard..."
                    rows={3}
                    required
                  />
                </div>

                {/* Difficulty */}
                <CustomSelect
                  options={difficultyOptions}
                  value={difficulty}
                  onChange={(value) => setDifficulty(value as 1 | 2 | 3)}
                  label="Dificuldade *"
                  placeholder="Selecionar dificuldade"
                />

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-blue-300 mb-2">
                Tags (opcional)
              </label>
              <div className="space-y-3">
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="flex-1 bg-blue-900/30 border border-blue-500/30 rounded-lg px-4 py-2 text-blue-100 
                      placeholder:text-blue-400 focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="Digite uma tag e pressione Enter..."
                  />
                  <Button
                    type="button"
                    onClick={addTag}
                    disabled={!newTag.trim()}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg"
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                
                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag, index) => (
                      <motion.span
                        key={index}
                        className="flex items-center gap-2 px-3 py-1 bg-blue-800/30 text-blue-300 rounded-full text-sm"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                      >
                        <Tag className="w-3 h-3" />
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="text-blue-400 hover:text-red-400 transition-colors"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </motion.span>
                    ))}
                  </div>
                )}
              </div>
            </div>
              </>
            ) : (
              <>
                {/* AI Creation Fields */}
                {/* Topic */}
                <div>
                  <label className="block text-sm font-medium text-purple-300 mb-2">
                    Tópico *
                  </label>
                  <input
                    type="text"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    className="w-full bg-purple-900/30 border border-purple-500/30 rounded-lg px-4 py-3 text-purple-100 
                      placeholder:text-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Ex: Biologia Celular, Matemática Básica, História do Brasil..."
                    required
                  />
                </div>

                {/* Number of Cards */}
                <CustomSelect
                  options={numberOfCardsOptions}
                  value={numberOfCards}
                  onChange={(value) => setNumberOfCards(value as number)}
                  label="Número de Flashcards (1-5) *"
                  placeholder="Selecionar quantidade"
                />

                {/* AI Difficulty */}
                <CustomSelect
                  options={aiDifficultyOptions}
                  value={aiDifficulty}
                  onChange={(value) => setAiDifficulty(value as 'fácil' | 'médio' | 'difícil')}
                  label="Dificuldade *"
                  placeholder="Selecionar dificuldade"
                />

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-purple-300 mb-2">
                    Tags (opcional)
                  </label>
                  <div className="space-y-3">
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyPress={handleKeyPress}
                        className="flex-1 bg-purple-900/30 border border-purple-500/30 rounded-lg px-4 py-2 text-purple-100 
                          placeholder:text-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                        placeholder="Digite uma tag e pressione Enter..."
                      />
                      <Button
                        type="button"
                        onClick={addTag}
                        disabled={!newTag.trim()}
                        className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    
                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {tags.map((tag, index) => (
                          <motion.span
                            key={index}
                            className="flex items-center gap-2 px-3 py-1 bg-purple-800/30 text-purple-300 rounded-full text-sm"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                          >
                            <Tag className="w-3 h-3" />
                            {tag}
                            <button
                              type="button"
                              onClick={() => removeTag(tag)}
                              className="text-purple-400 hover:text-red-400 transition-colors"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </motion.span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {/* Error Message */}
            {error && (
              <div className="flex items-center gap-2 text-red-400 text-sm bg-red-500/10 p-3 rounded-lg">
                <AlertCircle className="w-4 h-4" />
                {error}
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t border-blue-500/20">
              <Button
                type="button"
                onClick={onClose}
                variant="outline"
                className="border-blue-500/30 text-blue-300 hover:bg-blue-900/30"
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isLoading || (creationMode === 'manual' ? (!question.trim() || !answer.trim()) : !topic.trim())}
                className={`bg-gradient-to-r ${
                  creationMode === 'manual' 
                    ? 'from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700'
                    : 'from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700'
                }`}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    {creationMode === 'manual' ? 'Criando...' : 'Gerando com IA...'}
                  </>
                ) : (
                  <>
                    {creationMode === 'manual' ? (
                      <Plus className="w-4 h-4 mr-2" />
                    ) : (
                      <Wand2 className="w-4 h-4 mr-2" />
                    )}
                    {creationMode === 'manual' ? 'Criar Flashcard' : `Criar ${numberOfCards} Flashcards`}
                  </>
                )}
              </Button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
