import { useState, useEffect, useCallback } from 'react';
import <PERSON>card<PERSON>ontroller from '../../../../services/CDS/api/FlashcardController';
import { Deck as APIDeck } from '../../../../services/CDS/interfaces';
import { Deck, Flashcard, mapAPIDeckToUI, getUniqueSubjects, getUniqueYears } from '../utils/dataMapping';
import { useAuth } from '../../../../context/AuthContext';

interface UseFlashcardsReturn {
    decks: Deck[];
    subjects: string[];
    years: number[];
    loading: boolean;
    error: string | null;
    refreshDecks: () => Promise<void>;
    submitAnswers: (answers: { flashcardId: string; isCorrect: boolean }[]) => Promise<void>;
}

export const useFlashcards = (): UseFlashcardsReturn => {
    const { authData } = useAuth();
    const [decks, setDecks] = useState<Deck[]>([]);
    const [subjects, setSubjects] = useState<string[]>([]);
    const [years, setYears] = useState<number[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchDecks = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            console.log('Fetching decks for user:', authData);
            // Check if user is authenticated
            if (!authData?._id) {
                console.log('No authenticated user, skipping deck fetch');
                setDecks([]);
                setSubjects(['Matemática', 'Português', 'História', 'Geografia', 'Ciências', 'Inglês']);
                setYears([6, 7, 8, 9]);
                return;
            }
            
            // Fetch user's decks with pagination
            console.log('Making API call to getSelfDecks...');
            const response = await FlashcardController.getSelfDecks(0, 50); // Get first 50 decks
            
            console.log('API Response:', response);
            console.log('Response type:', typeof response);
            console.log('Is array:', Array.isArray(response));
            
            // Handle different response formats
            if (Array.isArray(response)) {
                // Direct array response
                const apiDecks: APIDeck[] = response;
                const uiDecks = apiDecks.map((apiDeck, index) => mapAPIDeckToUI(apiDeck, index));
                
                setDecks(uiDecks);
                setSubjects(getUniqueSubjects(uiDecks));
                setYears(getUniqueYears(uiDecks));
            } else if (response && response.data && Array.isArray(response.data)) {
                // Response with data property
                const apiDecks: APIDeck[] = response.data;
                const uiDecks = apiDecks.map((apiDeck, index) => mapAPIDeckToUI(apiDeck, index));
                
                setDecks(uiDecks);
                setSubjects(getUniqueSubjects(uiDecks));
                setYears(getUniqueYears(uiDecks));
            } else if (response && response.success === false) {
                // API returned an error response
                throw new Error(response.message || 'Failed to fetch decks');
            } else {
                // Empty or unexpected response - treat as no decks
                console.log('No decks found or unexpected response format');
                setDecks([]);
                setSubjects(['Matemática', 'Português', 'História', 'Geografia', 'Ciências', 'Inglês']);
                setYears([6, 7, 8, 9]);
            }
        } catch (err) {
            console.error('Error fetching decks:', err);
            const errorMessage = err instanceof Error ? err.message : 'Failed to fetch decks';
            
            // For now, let's treat API errors as "no decks available" instead of showing error
            // This allows users to create their first deck even if the API is down
            console.log('Treating API error as empty state to allow deck creation');
            setError(null); // Don't show error, show empty state instead
            setDecks([]);
            setSubjects(['Matemática', 'Português', 'História', 'Geografia', 'Ciências', 'Inglês']);
            setYears([6, 7, 8, 9]);
            
            // Uncomment the line below if you want to show errors instead of empty state
            // setError(errorMessage);
        } finally {
            setLoading(false);
        }
    }, [authData]);

    const submitAnswers = useCallback(async (answers: { flashcardId: string; isCorrect: boolean }[]) => {
        try {
            if (!authData?._id) {
                console.error('No authenticated user found');
                throw new Error('Usuário não autenticado');
            }
            
            if (answers.length === 0) {
                console.log('No answers to submit');
                return;
            }
            
            const flashcardAnswers = answers.map(answer => ({
                flashcardId: answer.flashcardId,
                isCorrect: answer.isCorrect,
                userId: authData._id
            }));

            console.log('Submitting answers:', flashcardAnswers);
            const response = await FlashcardController.batchAnswerFlashcards(flashcardAnswers);
            
            if (!response || (response.success === false)) {
                throw new Error(response?.message || 'Falha ao enviar respostas');
            }
            
            console.log('Successfully submitted answers:', response);
        } catch (err) {
            console.error('Error submitting answers:', err);
            // Re-throw the error so the UI can handle it
            throw err;
        }
    }, [authData]);

    useEffect(() => {
        fetchDecks();
    }, [fetchDecks]);

    return {
        decks,
        subjects,
        years,
        loading,
        error,
        refreshDecks: fetchDecks,
        submitAnswers
    };
};
