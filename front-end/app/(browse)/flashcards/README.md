# Flashcards Integration

This document describes the integration of the flashcards page with the real FlashcardController API, replacing the previously used mocked data.

## Changes Made

### 1. Data Mapping Utilities (`utils/dataMapping.ts`)
- Created mapping functions to convert between API data structures and UI data structures
- Added `apiId` fields to preserve original API IDs for submissions
- Mapped difficulty levels from API format (1-3) to UI format ('fácil', 'médio', 'difícil')

### 2. Custom Hook (`hooks/useFlashcards.ts`)
- Created `useFlashcards` hook to manage API calls and state
- Integrated with `AuthContext` for user authentication
- Added loading states and error handling
- Implemented answer submission to API

### 3. Main Component Updates (`page.tsx`)
- Replaced mocked data imports with real API integration
- Added loading and error states with proper UI feedback
- Updated all data filtering and deck selection to use real data
- Implemented answer tracking and submission to API
- Added proper null checks and error boundaries

## API Integration

The flashcards page now uses the following FlashcardController methods:

- `getSelfDecks()` - Fetches user's decks with pagination
- `batchAnswerFlashcards()` - Submits flashcard answers in batch

## Data Flow

1. **Loading**: Hook fetches user's decks on component mount
2. **Display**: Decks are mapped to UI format and displayed with filtering
3. **Interaction**: User studies flashcards and provides answers
4. **Submission**: Answers are tracked and submitted to API when deck is completed

## Error Handling

- Loading states with spinner animation
- Error states with retry functionality
- Graceful fallbacks for missing data
- Console logging for debugging

## Authentication

- Uses `AuthContext` to get current user information
- Validates user authentication before API calls
- Handles cases where user is not authenticated

## Future Improvements

- Add deck creation functionality
- Implement real-time statistics updates
- Add offline support with local storage
- Enhance error messages and user feedback
