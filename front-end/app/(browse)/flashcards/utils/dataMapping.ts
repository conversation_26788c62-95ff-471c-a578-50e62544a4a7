import { Flashcard as APIFlashcard, Deck as APIDeck, DeckFlashcard } from '../../../../services/CDS/interfaces';

// UI Data Types (matching the current component structure)
export type Difficulty = 'fácil' | 'médio' | 'difícil';

export interface Flashcard {
    id: number;
    apiId: string; // Store the original API ID for submissions
    frente: string;
    verso: string;
    dificuldade: Difficulty;
}

export interface Deck {
    id: number;
    apiId: string; // Store the original API ID
    titulo: string;
    descricao: string;
    materia: string;
    anosAplicaveis: number[];
    cards: Flashcard[];
}

// Mapping functions
export const mapAPIFlashcardToUI = (apiFlashcard: APIFlashcard, index: number): Flashcard => {
    return {
        id: index + 1, // Generate sequential ID for UI
        apiId: apiFlashcard.id || `flashcard-${index}`, // Store original API ID
        frente: apiFlashcard.question,
        verso: apiFlashcard.answer,
        dificuldade: mapDifficultyToUI(apiFlashcard.difficulty)
    };
};

export const mapAPIDeckToUI = (apiDeck: APIDeck, index: number): Deck => {
    // Sort flashcards by sortOrder
    const sortedFlashcards = apiDeck.flashcards.sort((a, b) => a.sortOrder - b.sortOrder);
    
    return {
        id: index + 1, // Generate sequential ID for UI
        apiId: apiDeck.id || `deck-${index}`, // Store original API ID
        titulo: apiDeck.name,
        descricao: apiDeck.description,
        materia: 'Geral', // Default since API doesn't have subject field
        anosAplicaveis: [6, 7, 8, 9], // Default to all years since API doesn't have year field
        cards: sortedFlashcards.map((deckFlashcard, cardIndex) => 
            mapAPIFlashcardToUI(deckFlashcard.flashcard, cardIndex)
        )
    };
};

export const mapDifficultyToUI = (apiDifficulty: 1 | 2 | 3): Difficulty => {
    switch (apiDifficulty) {
        case 1:
            return 'fácil';
        case 2:
            return 'médio';
        case 3:
            return 'difícil';
        default:
            return 'médio';
    }
};

export const mapDifficultyToAPI = (uiDifficulty: Difficulty): 1 | 2 | 3 => {
    switch (uiDifficulty) {
        case 'fácil':
            return 1;
        case 'médio':
            return 2;
        case 'difícil':
            return 3;
        default:
            return 2;
    }
};

// Helper function to get unique subjects from decks
export const getUniqueSubjects = (decks: Deck[]): string[] => {
    const subjects = new Set(decks.map(deck => deck.materia));
    return Array.from(subjects).sort();
};

// Helper function to get unique years from decks
export const getUniqueYears = (decks: Deck[]): number[] => {
    const years = new Set<number>();
    decks.forEach(deck => {
        deck.anosAplicaveis.forEach(year => years.add(year));
    });
    return Array.from(years).sort();
};
