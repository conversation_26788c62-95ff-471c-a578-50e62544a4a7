import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { IItemMetadata } from '../../../../services/SHOP/interfaces';
import { 
  ExternalLink, 
  Copy, 
  CheckCircle, 
  Gift
} from 'lucide-react';

interface RedeemedHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemName: string;
  itemMetadata: IItemMetadata;
}

const RedeemedHistoryModal: React.FC<RedeemedHistoryModalProps> = ({
  isOpen,
  onClose,
  itemName,
  itemMetadata
}) => {
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code).then(() => {
      setCopiedCode(code);
      setTimeout(() => setCopiedCode(null), 2000);
    }).catch(err => console.error('Failed to copy code: ', err));
  };

  // Find redemption info from movement history
  const getRedemptionInfo = () => {
    // First check if there's direct redemption info in metadata
    if (itemMetadata.redeemCode || itemMetadata.redeemUrl) {
      return {
        redeemCode: itemMetadata.redeemCode,
        redeemUrl: itemMetadata.redeemUrl
      };
    }

    // If not, look through movement history for redemption info
    const movementsWithRedemption = itemMetadata.movementHistory?.filter(
      movement => movement.redeemCode || movement.redeemUrl
    ) || [];

    if (movementsWithRedemption.length > 0) {
      // Get the most recent movement with redemption info
      const latestMovement = movementsWithRedemption[movementsWithRedemption.length - 1];
      return {
        redeemCode: latestMovement.redeemCode,
        redeemUrl: latestMovement.redeemUrl
      };
    }

    return null;
  };

  const redemptionInfo = getRedemptionInfo();


  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto bg-[#0D1A52] border-slate-700 text-slate-200">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-2xl font-bold text-cyan-400 flex items-center">
            <Gift className="mr-2 h-6 w-6" />
            Histórico de Resgate
          </DialogTitle>
          <p className="text-slate-400 text-sm">
            Detalhes do resgate para: <span className="text-cyan-300 font-medium">{itemName}</span>
          </p>
        </DialogHeader>

        <div className="space-y-6">
          {/* Redemption Info */}
          {redemptionInfo && (
            <div className="bg-purple-900/20 p-6 rounded-lg border border-purple-500/30">
              <h3 className="text-xl font-semibold text-purple-400 mb-4 flex items-center">
                <Gift className="mr-2 h-6 w-6" />
                Informações de Resgate
              </h3>
              <div className="space-y-4">
                {redemptionInfo.redeemCode && (
                  <div className="bg-slate-800/50 p-4 rounded-lg border border-slate-600">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <span className="text-slate-400 text-sm block mb-2">Código de Resgate:</span>
                        <div className="text-amber-400 font-mono text-xl font-bold">
                          {redemptionInfo.redeemCode}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCopyCode(redemptionInfo.redeemCode!)}
                        className="ml-4 border-slate-600 hover:bg-slate-700"
                      >
                        {copiedCode === redemptionInfo.redeemCode ? (
                          <CheckCircle className="h-4 w-4 text-green-400" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                )}
                
                {redemptionInfo.redeemUrl && (
                  <div className="bg-slate-800/50 p-4 rounded-lg border border-slate-600">
                    <span className="text-slate-400 text-sm block mb-3">URL de Resgate:</span>
                    <a
                      href={redemptionInfo.redeemUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-cyan-400 hover:text-cyan-300 underline flex items-center text-base font-medium"
                    >
                      Acessar URL de Resgate
                      <ExternalLink className="ml-2 h-4 w-4" />
                    </a>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* No redemption info message */}
          {!redemptionInfo && (
            <div className="text-center py-8 text-slate-400">
              <Gift className="mx-auto h-12 w-12 mb-3 opacity-50" />
              <p>Nenhuma informação de resgate disponível para este item.</p>
            </div>
          )}
        </div>

        <div className="flex justify-end pt-4 border-t border-slate-700">
          <Button
            onClick={onClose}
            className="bg-slate-700 hover:bg-slate-600 text-slate-200"
          >
            Fechar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RedeemedHistoryModal;
