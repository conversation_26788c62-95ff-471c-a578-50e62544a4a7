'use client'

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { IDoubtComment } from '@/services/SAPS/api/DoubtsController';
import { LazyDoubtCard } from './LazyDoubtCard';

interface VirtualizedDoubtListProps {
  doubts: IDoubtComment[];
  renderDoubtCard: (doubt: IDoubtComment, index: number) => React.ReactNode;
  itemsPerPage?: number;
  className?: string;
}

export const VirtualizedDoubtList: React.FC<VirtualizedDoubtListProps> = ({
  doubts,
  renderDoubtCard,
  itemsPerPage = 20,
  className = "grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6"
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  // Calculate pagination
  const totalPages = Math.ceil(doubts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentDoubts = doubts.slice(0, endIndex); // Show all items up to current page

  // Auto-load more when scrolling near bottom
  useEffect(() => {
    const handleScroll = () => {
      if (isLoading || currentPage >= totalPages) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      
      // Load more when user is 200px from bottom
      if (scrollTop + windowHeight >= documentHeight - 200) {
        setIsLoading(true);
        
        // Simulate loading delay for better UX
        setTimeout(() => {
          setCurrentPage(prev => Math.min(prev + 1, totalPages));
          setIsLoading(false);
        }, 300);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [currentPage, totalPages, isLoading]);

  // Memoize the rendered cards to prevent unnecessary re-renders
  const renderedCards = useMemo(() => {
    return currentDoubts.map((doubt, index) => (
      <LazyDoubtCard key={doubt.id} index={index}>
        {renderDoubtCard(doubt, index)}
      </LazyDoubtCard>
    ));
  }, [currentDoubts, renderDoubtCard]);

  const loadMoreManually = useCallback(() => {
    if (currentPage < totalPages && !isLoading) {
      setIsLoading(true);
      setTimeout(() => {
        setCurrentPage(prev => prev + 1);
        setIsLoading(false);
      }, 300);
    }
  }, [currentPage, totalPages, isLoading]);

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={className}
      >
        <AnimatePresence mode="popLayout">
          {renderedCards}
        </AnimatePresence>
      </motion.div>

      {/* Loading indicator */}
      {isLoading && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="flex justify-center py-8"
        >
          <div className="flex items-center gap-3 text-blue-300">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-300"></div>
            <span>Carregando mais dúvidas...</span>
          </div>
        </motion.div>
      )}

      {/* Load more button (fallback for users who prefer manual loading) */}
      {currentPage < totalPages && !isLoading && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-center py-6"
        >
          <button
            onClick={loadMoreManually}
            className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            Carregar mais dúvidas ({doubts.length - currentDoubts.length} restantes)
          </button>
        </motion.div>
      )}

      {/* Pagination info */}
      {doubts.length > itemsPerPage && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex justify-center text-sm text-blue-400"
        >
          Mostrando {currentDoubts.length} de {doubts.length} dúvidas
          {currentPage < totalPages && ` • Página ${currentPage} de ${totalPages}`}
        </motion.div>
      )}
    </div>
  );
};
