import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DoubtDetailModal } from '../DoubtDetailModal';
import { IDoubtComment } from '@/services/SAPS/api/DoubtsController';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock UI components
jest.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <h1 data-testid="dialog-title">{children}</h1>,
  DialogDescription: ({ children }: any) => <div data-testid="dialog-description">{children}</div>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props} data-testid="button">
      {children}
    </button>
  ),
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children }: any) => <span data-testid="badge">{children}</span>,
}));

jest.mock('@/components/ui/textarea', () => ({
  Textarea: (props: any) => <textarea data-testid="textarea" {...props} />,
}));

const mockDoubt: IDoubtComment = {
  id: 1,
  content: 'Esta é uma dúvida de teste sobre matemática.',
  userId: 'user123',
  username: 'João Silva',
  videoId: 456,
  doubtStatus: -1,
  createdAt: '2024-01-15T10:30:00Z',
  updatedAt: '2024-01-15T10:30:00Z',
  video: {
    id: 456,
    title: 'Introdução à Álgebra',
    description: 'Conceitos básicos de álgebra',
  },
};

describe('DoubtDetailModal', () => {
  const defaultProps = {
    doubt: mockDoubt,
    isOpen: true,
    onClose: jest.fn(),
    onRespond: jest.fn(),
    isResponding: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders modal when open', () => {
    render(<DoubtDetailModal {...defaultProps} />);
    
    expect(screen.getByTestId('dialog')).toBeInTheDocument();
    expect(screen.getByTestId('dialog-title')).toHaveTextContent('Detalhes da Dúvida #1');
  });

  it('does not render when closed', () => {
    render(<DoubtDetailModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
  });

  it('displays doubt information correctly', () => {
    render(<DoubtDetailModal {...defaultProps} />);
    
    expect(screen.getByText('João Silva')).toBeInTheDocument();
    expect(screen.getByText('Esta é uma dúvida de teste sobre matemática.')).toBeInTheDocument();
    expect(screen.getByText('Introdução à Álgebra')).toBeInTheDocument();
  });

  it('shows respond section for pending doubts', () => {
    render(<DoubtDetailModal {...defaultProps} />);
    
    expect(screen.getByText('Responder Dúvida')).toBeInTheDocument();
    expect(screen.getByTestId('textarea')).toBeInTheDocument();
  });

  it('does not show respond section for answered doubts', () => {
    const answeredDoubt = { ...mockDoubt, doubtStatus: 1 };
    render(<DoubtDetailModal {...defaultProps} doubt={answeredDoubt} />);
    
    expect(screen.queryByText('Responder Dúvida')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    render(<DoubtDetailModal {...defaultProps} />);
    
    const closeButtons = screen.getAllByTestId('button');
    const closeButton = closeButtons.find(button => 
      button.getAttribute('aria-label')?.includes('Fechar')
    );
    
    if (closeButton) {
      fireEvent.click(closeButton);
      expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
    }
  });

  it('handles response submission', async () => {
    render(<DoubtDetailModal {...defaultProps} />);
    
    const textarea = screen.getByTestId('textarea');
    fireEvent.change(textarea, { target: { value: 'Esta é minha resposta' } });
    
    const respondButton = screen.getByText('Responder');
    fireEvent.click(respondButton);
    
    await waitFor(() => {
      expect(defaultProps.onRespond).toHaveBeenCalledWith(mockDoubt);
    });
  });

  it('displays accessibility attributes correctly', () => {
    render(<DoubtDetailModal {...defaultProps} />);
    
    const dialog = screen.getByTestId('dialog-content');
    expect(dialog).toHaveAttribute('aria-describedby', 'doubt-detail-description');
    
    const textarea = screen.getByTestId('textarea');
    expect(textarea).toHaveAttribute('aria-label', 'Campo de resposta para a dúvida do estudante');
  });
});
