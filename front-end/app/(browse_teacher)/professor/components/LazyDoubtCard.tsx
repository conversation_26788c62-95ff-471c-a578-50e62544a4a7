'use client'

import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface LazyDoubtCardProps {
  children: React.ReactNode;
  index: number;
  threshold?: number;
}

export const LazyDoubtCard: React.FC<LazyDoubtCardProps> = ({ 
  children, 
  index, 
  threshold = 0.1 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasBeenVisible) {
          setIsVisible(true);
          setHasBeenVisible(true);
          // Disconnect observer after first intersection
          observer.disconnect();
        }
      },
      {
        threshold,
        rootMargin: '50px 0px', // Start loading 50px before the element comes into view
      }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => observer.disconnect();
  }, [threshold, hasBeenVisible]);

  // For the first few cards, render immediately to avoid layout shift
  const shouldRenderImmediately = index < 6;

  if (!shouldRenderImmediately && !isVisible && !hasBeenVisible) {
    return (
      <div ref={cardRef}>
        <DoubtCardSkeleton />
      </div>
    );
  }

  return (
    <div ref={cardRef}>
      {shouldRenderImmediately || hasBeenVisible ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            duration: 0.3, 
            delay: shouldRenderImmediately ? index * 0.05 : 0 
          }}
        >
          {children}
        </motion.div>
      ) : (
        <DoubtCardSkeleton />
      )}
    </div>
  );
};

const DoubtCardSkeleton: React.FC = () => {
  return (
    <Card className="bg-gradient-to-br from-[#0A1340]/90 to-[#1A237E]/90 border-blue-700/40 backdrop-blur-sm relative overflow-hidden">
      {/* Status indicator bar skeleton */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-blue-500/30" />
      
      <div className="p-5 space-y-4">
        {/* Header skeleton */}
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <div className="flex items-center gap-3 min-w-0">
              <Skeleton className="w-10 h-10 rounded-full bg-blue-600/30" />
              <div className="min-w-0">
                <Skeleton className="h-4 w-24 bg-blue-600/30 mb-1" />
                <Skeleton className="h-3 w-16 bg-blue-600/30" />
              </div>
            </div>
          </div>
          <div className="flex items-center gap-3 flex-shrink-0">
            <Skeleton className="h-6 w-20 bg-blue-600/30" />
            <Skeleton className="h-4 w-16 bg-blue-600/30" />
          </div>
        </div>

        {/* Video context skeleton */}
        <div className="flex items-center gap-3 p-3 bg-purple-900/20 rounded-lg border border-purple-700/30">
          <Skeleton className="h-4 w-4 bg-purple-600/30" />
          <div className="min-w-0 flex-1">
            <Skeleton className="h-4 w-32 bg-purple-600/30 mb-1" />
            <Skeleton className="h-3 w-20 bg-purple-600/30" />
          </div>
        </div>

        {/* Question content skeleton */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4 bg-blue-600/30" />
            <Skeleton className="h-4 w-32 bg-blue-600/30" />
          </div>
          <div className="bg-blue-950/30 p-4 rounded-lg border border-blue-800/30">
            <Skeleton className="h-4 w-full bg-blue-600/30 mb-2" />
            <Skeleton className="h-4 w-3/4 bg-blue-600/30 mb-2" />
            <Skeleton className="h-4 w-1/2 bg-blue-600/30" />
          </div>
        </div>

        {/* Actions skeleton */}
        <div className="flex justify-between items-center pt-3 border-t border-blue-700/30">
          <div className="flex items-center gap-4">
            <Skeleton className="h-3 w-16 bg-blue-600/30" />
            <Skeleton className="h-3 w-20 bg-blue-600/30" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-20 bg-blue-600/30" />
            <Skeleton className="h-8 w-24 bg-blue-600/30" />
          </div>
        </div>
      </div>
    </Card>
  );
};
