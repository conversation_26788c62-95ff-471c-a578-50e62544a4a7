'use client'

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Clock,
  CheckCircle,
  AlertCircle,
  HelpCircle,
  MessageSquare,
  User,
  PlayCircle,
  Filter,
  Zap,
  Star,
  Reply,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useUserInformation } from '@/context/UserContext';
import TeacherManagerController from '@/services/CDS/api/TeacherManagerController';
import DoubtsController, { IDoubtComment } from '@/services/SAPS/api/DoubtsController';
import { ITeacherGroup } from '@/services/CDS/interfaces/teacherManager';
import { Skeleton } from '@/components/ui/skeleton';
import { DoubtDetailModal } from './DoubtDetailModal';
import { VirtualizedDoubtList } from './VirtualizedDoubtList';

interface TeacherDoubtsProps {
  // Props opcionais para personalização se necessário
}

export const TeacherDoubts: React.FC<TeacherDoubtsProps> = () => {
  const router = useRouter();
  const { authData } = useAuth();
  const { user } = useUserInformation();
  const [doubts, setDoubts] = useState<IDoubtComment[]>([]);
  const [groups, setGroups] = useState<ITeacherGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'answered'>('all');
  const [respondingToDoubt, setRespondingToDoubt] = useState<number | null>(null);
  const [selectedDoubt, setSelectedDoubt] = useState<IDoubtComment | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(Date.now());

  useEffect(() => {
    const fetchDoubtsData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Busca os grupos do professor
        const groupsResponse = await TeacherManagerController.getTeacherGroups('current', true, 1, 100, true);
        setGroups(groupsResponse.groups);

        // Extrai os IDs dos grupos
        const groupIds = groupsResponse.groups.map((group: ITeacherGroup) => group.id);

        if (groupIds.length > 0) {
          const startTime = performance.now();

          // Use cached version with longer timeout for better performance
          const doubtsData = await DoubtsController.getDoubtsFromMultipleGroupsCached(groupIds, {
            limit: 200, // Increased limit for better pagination
            cacheTimeout: 60000 // 1 minute cache
          });

          const endTime = performance.now();
          console.log(`[TeacherDoubts] Request completed in ${Math.round(endTime - startTime)}ms, found ${doubtsData.length} doubts`);

          setDoubts(doubtsData);
        }
      } catch (err: any) {
        console.error('Erro ao buscar dúvidas:', err);
        setError('Erro ao carregar dúvidas. Tente novamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchDoubtsData();
  }, [lastRefresh]);

  const handleRefresh = () => {
    // Clear cache and refresh data
    DoubtsController.clearCache();
    setLastRefresh(Date.now());
  };

  const getStatusInfo = (status: number) => {
    switch (status) {
      case -1:
        return { label: 'Pendente', color: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30', icon: Clock };
      case 0:
        return { label: 'Em andamento', color: 'bg-blue-500/20 text-blue-300 border-blue-500/30', icon: AlertCircle };
      case 1:
        return { label: 'Respondida', color: 'bg-green-500/20 text-green-300 border-green-500/30', icon: CheckCircle };
      default:
        return { label: 'Desconhecido', color: 'bg-gray-500/20 text-gray-300 border-gray-500/30', icon: Clock };
    }
  };

  const filteredDoubts = doubts.filter(doubt => {
    const matchesSearch = (doubt.content || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doubt.username || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doubt.video?.title || '').toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'pending' && doubt.doubtStatus === -1) ||
      (statusFilter === 'answered' && doubt.doubtStatus === 1);

    return matchesSearch && matchesStatus;
  });

  const handleViewDoubt = (doubt: IDoubtComment) => {
    setSelectedDoubt(doubt);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedDoubt(null);
  };

  const renderDoubtCard = (doubt: IDoubtComment, index: number) => {
    const statusInfo = getStatusInfo(doubt.doubtStatus);
    const StatusIcon = statusInfo.icon;

    return (
      <motion.div
        key={doubt.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.05 }}
        whileHover={{ scale: 1.01, y: -2 }}
        className="group"
      >
        <Card
          className="bg-gradient-to-br from-[#0A1340]/90 to-[#1A237E]/90 border-blue-700/40 backdrop-blur-sm transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/25 hover:border-blue-600/60 relative overflow-hidden"
          role="article"
          aria-labelledby={`doubt-title-${doubt.id}`}
          aria-describedby={`doubt-content-${doubt.id}`}
          tabIndex={0}
        >
          {/* Status indicator bar */}
          <div
            className={`absolute top-0 left-0 right-0 h-1 ${statusInfo.color.includes('yellow') ? 'bg-gradient-to-r from-yellow-500/80 to-transparent' : statusInfo.color.includes('blue') ? 'bg-gradient-to-r from-blue-500/80 to-transparent' : 'bg-gradient-to-r from-green-500/80 to-transparent'}`}
            aria-hidden="true"
          />

          <div className="p-5 space-y-4">
            {/* Header with student info and status */}
            <div className="flex justify-between items-start">
              <div className="flex items-center gap-3 min-w-0 flex-1">
                <div className="flex items-center gap-3 min-w-0">
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-600/30 rounded-full flex items-center justify-center border border-blue-500/40">
                    <User className="h-5 w-5 text-blue-200" aria-hidden="true" />
                  </div>
                  <div className="min-w-0">
                    <span
                      id={`doubt-title-${doubt.id}`}
                      className="font-semibold text-blue-100 block truncate"
                    >
                      {doubt.username}
                    </span>
                    <span className="text-xs text-blue-400 block">Estudante</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3 flex-shrink-0">
                <motion.div whileHover={{ scale: 1.05 }}>
                  <Badge className={`${statusInfo.color} border-0 flex items-center gap-1 px-3 py-1 font-medium`}>
                    <StatusIcon className="h-3 w-3" />
                    {statusInfo.label}
                  </Badge>
                </motion.div>
                <div className="flex items-center gap-1 text-blue-400 text-xs">
                  <Clock className="h-3 w-3" />
                  <span className="whitespace-nowrap">{formatDateTime(doubt.createdAt)}</span>
                </div>
              </div>
            </div>

            {/* Video context */}
            {doubt.video && (
              <div className="flex items-center gap-3 p-3 bg-purple-900/20 rounded-lg border border-purple-700/30">
                <PlayCircle className="h-4 w-4 text-purple-300 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <span className="text-sm text-purple-200 font-medium block truncate">{doubt.video.title}</span>
                  <span className="text-xs text-purple-400">Vídeo da aula</span>
                </div>
              </div>
            )}

            {/* Question content */}
            <div className="space-y-2">
              <h4 className="text-blue-200 font-medium text-sm flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Dúvida do estudante:
              </h4>
              <div
                id={`doubt-content-${doubt.id}`}
                className="text-blue-50 text-sm leading-relaxed bg-blue-950/30 p-4 rounded-lg border border-blue-800/30"
              >
                {doubt.content.length > 200 ? (
                  <>
                    {doubt.content.substring(0, 200)}
                    <span className="text-blue-400">... </span>
                    <button
                      className="text-blue-300 hover:text-blue-200 underline text-xs"
                      onClick={() => handleViewDoubt(doubt)}
                      aria-label={`Ver conteúdo completo da dúvida de ${doubt.username}`}
                    >
                      ver mais
                    </button>
                  </>
                ) : (
                  doubt.content
                )}
              </div>
            </div>

            {/* Actions and metadata */}
            <div className="flex justify-between items-center pt-3 border-t border-blue-700/30">
              <div className="flex items-center gap-4 text-xs text-blue-400">
                <div className="flex items-center gap-1">
                  <MessageSquare className="h-3 w-3" />
                  <span>ID: {doubt.id}</span>
                </div>
                {doubt.video && (
                  <div className="flex items-center gap-1">
                    <PlayCircle className="h-3 w-3" />
                    <span>Vídeo: {doubt.video.id}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-2">
                {/* Respond button - only show for pending doubts (status -1) */}
                {doubt.doubtStatus === -1 && (
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      size="sm"
                      variant="default"
                      className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white border-0 shadow-lg"
                      onClick={() => handleRespondToDoubt(doubt)}
                      disabled={respondingToDoubt === doubt.id}
                      aria-label={`Responder à dúvida de ${doubt.username}`}
                      aria-describedby={respondingToDoubt === doubt.id ? `responding-${doubt.id}` : undefined}
                    >
                      {respondingToDoubt === doubt.id ? (
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" aria-hidden="true" />
                      ) : (
                        <Reply className="h-3 w-3 mr-1" aria-hidden="true" />
                      )}
                      {respondingToDoubt === doubt.id ? 'Respondendo...' : 'Responder'}
                      {respondingToDoubt === doubt.id && (
                        <span id={`responding-${doubt.id}`} className="sr-only">
                          Processando resposta para a dúvida
                        </span>
                      )}
                    </Button>
                  </motion.div>
                )}

                {/* View button - Opens modal */}
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    size="sm"
                    variant="outline"
                    className="text-blue-300 hover:text-blue-100 hover:bg-blue-600/20 border-blue-600/40 hover:border-blue-500/60"
                    onClick={() => handleViewDoubt(doubt)}
                    aria-label={`Visualizar detalhes da dúvida de ${doubt.username}`}
                  >
                    <Star className="h-3 w-3 mr-1" aria-hidden="true" />
                    Visualizar
                  </Button>
                </motion.div>
              </div>
            </div>
          </div>
        </Card>
      </motion.div>
    );
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleRespondToDoubt = async (doubt: IDoubtComment) => {
    if (respondingToDoubt === doubt.id) return; // Prevent double-click

    setRespondingToDoubt(doubt.id);

    try {
      console.log(`[TeacherDoubts] Responding to doubt ${doubt.id}`);

      const response = await DoubtsController.respondToDoubt(
        doubt.id,
        user?.name || 'Professor'
      );

      if (response.success && response.data.channelId) {
        console.log(`[TeacherDoubts] Successfully created channel ${response.data.channelId}`);

        // Update doubt status locally to reflect the change
        setDoubts(prevDoubts =>
          prevDoubts.map(d =>
            d.id === doubt.id
              ? { ...d, doubtStatus: 0 as const } // Teacher responding
              : d
          )
        );

        // Navigate to chat with the created channel
        router.push(`/chat?channel=${response.data.channelId}`);
      }
    } catch (error: any) {
      console.error(`[TeacherDoubts] Failed to respond to doubt ${doubt.id}:`, error);

      // Show error message to user
      const errorMessage = error.response?.data?.message || error.message || 'Erro ao responder à dúvida';
      alert(`Erro: ${errorMessage}`);
    } finally {
      setRespondingToDoubt(null);
    }
  };

  return (
    <div className="space-y-6" role="main" aria-label="Painel de dúvidas dos estudantes">
      {/* Header */}
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
            Dúvidas dos Alunos
          </h1>
          <p className="text-blue-400 text-sm mt-1" id="page-description">
            Gerencie e responda às dúvidas dos seus estudantes
          </p>
        </div>
        <div className="flex items-center gap-3">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
              className="border-blue-600/40 text-blue-300 hover:bg-blue-600/20"
              aria-label={loading ? "Atualizando dúvidas..." : "Atualizar lista de dúvidas"}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} aria-hidden="true" />
              Atualizar
            </Button>
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Badge
              variant="secondary"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 px-4 py-2"
              aria-label={`Total de ${filteredDoubts.length} dúvidas encontradas`}
            >
              <HelpCircle className="h-4 w-4 mr-1" aria-hidden="true" />
              {filteredDoubts.length} dúvidas
            </Badge>
          </motion.div>
        </div>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        role="search"
        aria-label="Filtros de busca para dúvidas"
      >
        <Card className="bg-gradient-to-r from-[#0A1340]/80 to-[#1A237E]/80 border-blue-700/50 p-6 backdrop-blur-sm">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-blue-400" aria-hidden="true" />
                <Input
                  placeholder="Buscar por conteúdo, autor ou vídeo..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-[#061037]/80 border-blue-600/50 text-blue-100 placeholder-blue-400 h-11 focus:border-blue-400"
                  aria-label="Campo de busca para filtrar dúvidas por conteúdo, autor ou vídeo"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  variant={statusFilter === 'all' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('all')}
                  size="sm"
                  className={`${statusFilter === 'all'
                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 border-0'
                    : 'border-blue-600/50 text-blue-300 hover:bg-blue-600/20'
                    }`}
                >
                  <Filter className="h-3 w-3 mr-1" />
                  Todas
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  variant={statusFilter === 'pending' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('pending')}
                  size="sm"
                  className={`${statusFilter === 'pending'
                    ? 'bg-gradient-to-r from-yellow-600 to-yellow-700 border-0'
                    : 'border-yellow-600/50 text-yellow-300 hover:bg-yellow-600/20'
                    }`}
                >
                  <Clock className="h-3 w-3 mr-1" />
                  Pendentes
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  variant={statusFilter === 'answered' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('answered')}
                  size="sm"
                  className={`${statusFilter === 'answered'
                    ? 'bg-gradient-to-r from-green-600 to-green-700 border-0'
                    : 'border-green-600/50 text-green-300 hover:bg-green-600/20'
                    }`}
                >
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Respondidas
                </Button>
              </motion.div>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Content */}
      <AnimatePresence mode="wait">
        {loading ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            {[...Array(5)].map((_, i) => (
              <Card key={i} className="bg-gradient-to-r from-[#0A1340]/60 to-[#1A237E]/60 border-blue-700/30 p-6 backdrop-blur-sm">
                <div className="space-y-4">
                  <div className="flex justify-between items-start">
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-32 bg-blue-600/30" />
                      <Skeleton className="h-6 w-20 bg-blue-600/30" />
                    </div>
                    <Skeleton className="h-8 w-24 bg-blue-600/30" />
                  </div>
                  <Skeleton className="h-16 w-full bg-blue-600/30" />
                  <div className="flex justify-between items-center">
                    <Skeleton className="h-4 w-24 bg-blue-600/30" />
                    <Skeleton className="h-4 w-32 bg-blue-600/30" />
                  </div>
                </div>
              </Card>
            ))}
          </motion.div>
        ) : error ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            role="alert"
            aria-live="polite"
          >
            <Card className="bg-gradient-to-r from-red-900/30 to-red-800/30 border-red-600/50 p-8 text-center backdrop-blur-sm">
              <motion.div
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" aria-hidden="true" />
              </motion.div>
              <h3 className="text-xl font-semibold mb-2 text-red-300">Erro ao carregar dúvidas</h3>
              <p className="text-red-400 mb-6 max-w-md mx-auto">
                {error}. Verifique sua conexão com a internet e tente novamente.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={handleRefresh}
                  className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 border-0"
                  aria-label="Tentar carregar as dúvidas novamente"
                >
                  <Zap className="h-4 w-4 mr-2" aria-hidden="true" />
                  Tentar novamente
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setError(null)}
                  className="border-red-600/40 text-red-300 hover:bg-red-600/20"
                  aria-label="Fechar mensagem de erro"
                >
                  Fechar
                </Button>
              </div>
            </Card>
          </motion.div>
        ) : filteredDoubts.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            role="status"
            aria-live="polite"
          >
            <Card className="bg-gradient-to-r from-[#0A1340]/60 to-[#1A237E]/60 border-blue-700/30 p-12 text-center backdrop-blur-sm">
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <HelpCircle className="h-16 w-16 text-blue-400 mx-auto mb-6" aria-hidden="true" />
              </motion.div>
              <h3 className="text-2xl font-semibold mb-3 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
                {searchTerm || statusFilter !== 'all' ? 'Nenhuma dúvida encontrada' : 'Nenhuma dúvida ainda'}
              </h3>
              <p className="text-blue-400 mb-6 max-w-md mx-auto">
                {searchTerm || statusFilter !== 'all'
                  ? 'Tente ajustar os filtros de busca ou limpar os termos para ver todas as dúvidas.'
                  : 'Quando os estudantes enviarem dúvidas, elas aparecerão aqui para você responder.'}
              </p>
              {(searchTerm || statusFilter !== 'all') && (
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button
                    onClick={() => {
                      setSearchTerm('');
                      setStatusFilter('all');
                    }}
                    variant="outline"
                    className="border-blue-600/40 text-blue-300 hover:bg-blue-600/20"
                    aria-label="Limpar todos os filtros de busca"
                  >
                    <Filter className="h-4 w-4 mr-2" aria-hidden="true" />
                    Limpar filtros
                  </Button>
                  <Button
                    onClick={handleRefresh}
                    variant="outline"
                    className="border-blue-600/40 text-blue-300 hover:bg-blue-600/20"
                    aria-label="Atualizar lista de dúvidas"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" aria-hidden="true" />
                    Atualizar
                  </Button>
                </div>
              )}
            </Card>
          </motion.div>
        ) : (
          <VirtualizedDoubtList
            doubts={filteredDoubts}
            renderDoubtCard={renderDoubtCard}
            itemsPerPage={15}
          />
        )}
      </AnimatePresence>

      {/* Doubt Detail Modal */}
      <DoubtDetailModal
        doubt={selectedDoubt}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onRespond={handleRespondToDoubt}
        isResponding={respondingToDoubt === selectedDoubt?.id}
      />
    </div>
  );
};
