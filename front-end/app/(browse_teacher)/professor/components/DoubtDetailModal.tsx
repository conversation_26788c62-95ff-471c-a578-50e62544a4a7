'use client'

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  User, 
  PlayCircle, 
  Clock, 
  MessageSquare, 
  Reply, 
  CheckCircle, 
  AlertCircle, 
  X,
  Send,
  Loader2,
  Calendar,
  Video
} from 'lucide-react';
import { IDoubtComment } from '@/services/SAPS/api/DoubtsController';

interface DoubtDetailModalProps {
  doubt: IDoubtComment | null;
  isOpen: boolean;
  onClose: () => void;
  onRespond?: (doubt: IDoubtComment) => void;
  isResponding?: boolean;
}

export const DoubtDetailModal: React.FC<DoubtDetailModalProps> = ({
  doubt,
  isOpen,
  onClose,
  onRespond,
  isResponding = false
}) => {
  const [response, setResponse] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!doubt) return null;

  const getStatusInfo = (status: number) => {
    switch (status) {
      case -1:
        return { 
          label: 'Pendente', 
          color: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30', 
          icon: Clock,
          description: 'Esta dúvida ainda não foi respondida'
        };
      case 0:
        return { 
          label: 'Em andamento', 
          color: 'bg-blue-500/20 text-blue-300 border-blue-500/30', 
          icon: AlertCircle,
          description: 'Um professor está trabalhando nesta dúvida'
        };
      case 1:
        return { 
          label: 'Respondida', 
          color: 'bg-green-500/20 text-green-300 border-green-500/30', 
          icon: CheckCircle,
          description: 'Esta dúvida já foi respondida'
        };
      default:
        return { 
          label: 'Desconhecido', 
          color: 'bg-gray-500/20 text-gray-300 border-gray-500/30', 
          icon: Clock,
          description: 'Status desconhecido'
        };
    }
  };

  const statusInfo = getStatusInfo(doubt.doubtStatus);
  const StatusIcon = statusInfo.icon;

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleRespond = async () => {
    if (!onRespond || !response.trim()) return;
    
    setIsSubmitting(true);
    try {
      await onRespond(doubt);
      setResponse('');
      onClose();
    } catch (error) {
      console.error('Error responding to doubt:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-4xl bg-gradient-to-br from-[#0A1340]/95 to-[#1A237E]/95 border-blue-500/30 backdrop-blur-md max-h-[90vh] overflow-y-auto"
        aria-describedby="doubt-detail-description"
      >
        <DialogHeader className="space-y-4">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
              Detalhes da Dúvida #{doubt.id}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-blue-300 hover:text-blue-100 hover:bg-blue-600/20"
              aria-label="Fechar modal de detalhes da dúvida"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Fechar</span>
            </Button>
          </div>
          
          <div className="flex items-center gap-3">
            <Badge
              className={`${statusInfo.color} border-0 flex items-center gap-1 px-3 py-1`}
              aria-label={`Status da dúvida: ${statusInfo.label}`}
            >
              <StatusIcon className="h-3 w-3" aria-hidden="true" />
              {statusInfo.label}
            </Badge>
            <span className="text-sm text-blue-400" id="doubt-detail-description">
              {statusInfo.description}
            </span>
          </div>
        </DialogHeader>

        <div className="space-y-6 mt-6">
          {/* Student Information */}
          <section
            className="bg-blue-950/30 rounded-lg p-4 border border-blue-800/30"
            aria-labelledby="student-info-heading"
          >
            <h3 id="student-info-heading" className="text-blue-200 font-medium mb-3 flex items-center gap-2">
              <User className="h-4 w-4" aria-hidden="true" />
              Informações do Estudante
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-blue-400 text-sm">Nome:</span>
                <p className="text-blue-100 font-medium">{doubt.username}</p>
              </div>
              <div>
                <span className="text-blue-400 text-sm">Data da dúvida:</span>
                <p className="text-blue-100 flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {formatDateTime(doubt.createdAt)}
                </p>
              </div>
            </div>
          </section>

          {/* Video Context */}
          {doubt.video && (
            <div className="bg-purple-950/30 rounded-lg p-4 border border-purple-800/30">
              <h3 className="text-purple-200 font-medium mb-3 flex items-center gap-2">
                <Video className="h-4 w-4" />
                Contexto da Aula
              </h3>
              <div className="flex items-center gap-3">
                <PlayCircle className="h-5 w-5 text-purple-300 flex-shrink-0" />
                <div>
                  <p className="text-purple-100 font-medium">{doubt.video.title}</p>
                  {doubt.video.description && (
                    <p className="text-purple-300 text-sm mt-1">{doubt.video.description}</p>
                  )}
                  <p className="text-purple-400 text-xs mt-1">ID do vídeo: {doubt.video.id}</p>
                </div>
              </div>
            </div>
          )}

          {/* Question Content */}
          <div className="bg-blue-950/30 rounded-lg p-4 border border-blue-800/30">
            <h3 className="text-blue-200 font-medium mb-3 flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Dúvida do Estudante
            </h3>
            <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700/30">
              <p className="text-blue-50 leading-relaxed whitespace-pre-wrap">
                {doubt.content}
              </p>
            </div>
          </div>

          {/* Response Section */}
          {doubt.doubtStatus === -1 && onRespond && (
            <div className="bg-green-950/30 rounded-lg p-4 border border-green-800/30">
              <h3 className="text-green-200 font-medium mb-3 flex items-center gap-2">
                <Reply className="h-4 w-4" />
                Responder Dúvida
              </h3>
              <div className="space-y-3">
                <Textarea
                  placeholder="Digite sua resposta para o estudante..."
                  value={response}
                  onChange={(e) => setResponse(e.target.value)}
                  className="bg-green-900/20 border-green-700/40 text-green-50 placeholder:text-green-400/60 min-h-[100px]"
                  aria-label="Campo de resposta para a dúvida do estudante"
                  aria-describedby="response-help"
                />
                <div id="response-help" className="sr-only">
                  Digite sua resposta para ajudar o estudante com sua dúvida
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={onClose}
                    className="border-green-600/40 text-green-300 hover:bg-green-600/20"
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={handleRespond}
                    disabled={!response.trim() || isSubmitting || isResponding}
                    className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                  >
                    {isSubmitting || isResponding ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Respondendo...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Responder
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Metadata */}
          <div className="bg-gray-950/30 rounded-lg p-4 border border-gray-800/30">
            <h3 className="text-gray-200 font-medium mb-3">Metadados</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-400">ID da dúvida:</span>
                <p className="text-gray-200 font-mono">{doubt.id}</p>
              </div>
              <div>
                <span className="text-gray-400">Criado em:</span>
                <p className="text-gray-200">{formatDateTime(doubt.createdAt)}</p>
              </div>
              <div>
                <span className="text-gray-400">Atualizado em:</span>
                <p className="text-gray-200">{formatDateTime(doubt.updatedAt)}</p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
