'use client'

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  User,
  PlayCircle,
  Clock,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  Calendar,
  Video
} from 'lucide-react';
import { IDoubtComment } from '@/services/SAPS/api/DoubtsController';

interface DoubtDetailModalProps {
  doubt: IDoubtComment | null;
  isOpen: boolean;
  onClose: () => void;
  onRespond?: (doubt: IDoubtComment) => void;
  isResponding?: boolean;
}

export const DoubtDetailModal: React.FC<DoubtDetailModalProps> = ({
  doubt,
  isOpen,
  onClose,
  onRespond,
  isResponding = false
}) => {

  if (!doubt) return null;

  const isPlaceholderContent = (content: string) => {
    return content && (
      content.includes('temporarily unavailable') ||
      content.includes('Invalid comment ID') ||
      content.startsWith('[') && content.endsWith(']')
    );
  };

  const formatDoubtContent = (content: string) => {
    if (isPlaceholderContent(content)) {
      return (
        <div className="text-yellow-300 italic">
          <span className="text-yellow-400 text-lg">⚠️</span> Conteúdo temporariamente indisponível
          <div className="text-sm text-yellow-400 mt-2">
            Esta dúvida pode ter sido removida ou há um problema de sincronização entre os sistemas.
            Entre em contato com o suporte técnico se este problema persistir.
          </div>
        </div>
      );
    }
    return content;
  };

  const getStatusInfo = (status: number) => {
    switch (status) {
      case -1:
        return { 
          label: 'Pendente', 
          color: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30', 
          icon: Clock,
          description: 'Esta dúvida ainda não foi respondida'
        };
      case 0:
        return { 
          label: 'Em andamento', 
          color: 'bg-blue-500/20 text-blue-300 border-blue-500/30', 
          icon: AlertCircle,
          description: 'Um professor está trabalhando nesta dúvida'
        };
      case 1:
        return { 
          label: 'Respondida', 
          color: 'bg-green-500/20 text-green-300 border-green-500/30', 
          icon: CheckCircle,
          description: 'Esta dúvida já foi respondida'
        };
      default:
        return { 
          label: 'Desconhecido', 
          color: 'bg-gray-500/20 text-gray-300 border-gray-500/30', 
          icon: Clock,
          description: 'Status desconhecido'
        };
    }
  };

  const statusInfo = getStatusInfo(doubt.doubtStatus);
  const StatusIcon = statusInfo.icon;

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-4xl bg-gradient-to-br from-[#0A1340]/95 to-[#1A237E]/95 border-blue-500/30 backdrop-blur-md max-h-[90vh] overflow-y-auto"
        aria-describedby="doubt-detail-description"
      >
        <DialogHeader className="space-y-4">
          <DialogTitle className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
            Detalhes da Dúvida #{doubt.id}
          </DialogTitle>
          
          <div className="flex items-center gap-3">
            <Badge
              className={`${statusInfo.color} border-0 flex items-center gap-1 px-3 py-1`}
              aria-label={`Status da dúvida: ${statusInfo.label}`}
            >
              <StatusIcon className="h-3 w-3" aria-hidden="true" />
              {statusInfo.label}
            </Badge>
            <span className="text-sm text-blue-400" id="doubt-detail-description">
              {statusInfo.description}
            </span>
          </div>
        </DialogHeader>

        <div className="space-y-6 mt-6">
          {/* Student Information */}
          <section
            className="bg-blue-950/30 rounded-lg p-4 border border-blue-800/30"
            aria-labelledby="student-info-heading"
          >
            <h3 id="student-info-heading" className="text-blue-200 font-medium mb-3 flex items-center gap-2">
              <User className="h-4 w-4" aria-hidden="true" />
              Informações do Estudante
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-blue-400 text-sm">Nome:</span>
                <p className="text-blue-100 font-medium">{doubt.username}</p>
              </div>
              <div>
                <span className="text-blue-400 text-sm">Data da dúvida:</span>
                <p className="text-blue-100 flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {formatDateTime(doubt.createdAt)}
                </p>
              </div>
            </div>
          </section>

          {/* Video Context */}
          {doubt.video && (
            <div className="bg-purple-950/30 rounded-lg p-4 border border-purple-800/30">
              <h3 className="text-purple-200 font-medium mb-3 flex items-center gap-2">
                <Video className="h-4 w-4" />
                Contexto da Aula
              </h3>
              <div className="flex items-center gap-3">
                <PlayCircle className="h-5 w-5 text-purple-300 flex-shrink-0" />
                <div>
                  <p className="text-purple-100 font-medium">{doubt.video.title}</p>
                  {doubt.video.description && (
                    <p className="text-purple-300 text-sm mt-1">{doubt.video.description}</p>
                  )}
                  <p className="text-purple-400 text-xs mt-1">ID do vídeo: {doubt.video.id}</p>
                </div>
              </div>
            </div>
          )}

          {/* Question Content */}
          <div className="bg-blue-950/30 rounded-lg p-4 border border-blue-800/30">
            <h3 className="text-blue-200 font-medium mb-3 flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Dúvida do Estudante
            </h3>
            <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700/30">
              <div className="text-blue-50 leading-relaxed whitespace-pre-wrap">
                {formatDoubtContent(doubt.content)}
              </div>
            </div>
          </div>



          {/* Metadata */}
          <div className="bg-gray-950/30 rounded-lg p-4 border border-gray-800/30">
            <h3 className="text-gray-200 font-medium mb-3">Metadados</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-400">ID da dúvida:</span>
                <p className="text-gray-200 font-mono">{doubt.id}</p>
              </div>
              <div>
                <span className="text-gray-400">Criado em:</span>
                <p className="text-gray-200">{formatDateTime(doubt.createdAt)}</p>
              </div>
              <div>
                <span className="text-gray-400">Atualizado em:</span>
                <p className="text-gray-200">{formatDateTime(doubt.updatedAt)}</p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
