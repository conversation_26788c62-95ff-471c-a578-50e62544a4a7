"use client"

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';

interface SelectOption {
    value: string | number;
    label: string;
}

interface CustomSelectProps {
    options: SelectOption[];
    value: string | number | '';
    onChange: (value: string | number | '') => void;
    placeholder?: string;
    label?: string;
    className?: string;
    disabled?: boolean;
}

export function CustomSelect({
    options,
    value,
    onChange,
    placeholder = "Selecionar...",
    label,
    className = "",
    disabled = false
}: CustomSelectProps) {
    const [isOpen, setIsOpen] = useState(false);
    const selectRef = useRef<HTMLDivElement>(null);

    // Close when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as Node;
            if (selectRef.current && !selectRef.current.contains(target)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const selectedOption = options.find(option => option.value === value);

    const handleSelect = (optionValue: string | number | '') => {
        onChange(optionValue);
        setIsOpen(false);
    };


    return (
        <div className={`relative ${className}`} ref={selectRef} style={{ zIndex: 9999999, isolation: 'isolate' }}>
            {label && (
                <label className="block text-sm font-medium text-blue-300 mb-2">
                    {label}
                </label>
            )}
            
            <motion.button
                type="button"
                className={`w-full bg-blue-900/30 border border-blue-500/30 rounded-lg px-3 py-2 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm flex items-center justify-between transition-all duration-200 ${
                    disabled 
                        ? 'opacity-50 cursor-not-allowed' 
                        : 'hover:border-blue-400/50 cursor-pointer'
                }`}
                onClick={() => {
                    if (!disabled) {
                        console.log('CustomSelect clicked, isOpen:', isOpen, 'options:', options);
                        setIsOpen(!isOpen);
                    }
                }}
                disabled={disabled}
                whileHover={!disabled ? {} : {}}
                whileTap={!disabled ? { scale: 0.99 } : {}}
            >
                <span className={`${!selectedOption ? 'text-blue-400' : 'text-blue-100'}`}>
                    {selectedOption ? selectedOption.label : placeholder}
                </span>
                <motion.div
                    animate={{ rotate: isOpen ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                >
                    <ChevronDown className="w-4 h-4 text-blue-400" />
                </motion.div>
            </motion.button>

            <AnimatePresence>
                {isOpen && (
                    <motion.div
                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-full left-0 right-0 mt-1 bg-[#0F2057] border border-blue-500/30 rounded-lg shadow-2xl backdrop-blur-lg z-50"
                        style={{ zIndex: 9999 }}
                    >
                        <div className="max-h-48 overflow-y-auto">
                            {options.map((option, index) => (
                                <motion.button
                                    key={option.value}
                                    type="button"
                                    className={`w-full px-3 py-2 text-left text-sm transition-all duration-200 first:rounded-t-lg last:rounded-b-lg ${
                                        option.value === value
                                            ? 'bg-blue-600/30 text-blue-200 border-l-2 border-blue-400'
                                            : 'text-blue-100 hover:bg-blue-800/30 hover:text-blue-200'
                                    }`}
                                    onClick={() => handleSelect(option.value)}
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: index * 0.05 }}
                                >
                                    {option.label}
                                </motion.button>
                            ))}
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
}
