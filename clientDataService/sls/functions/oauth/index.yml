# OAuth Google Authentication Functions

# OAuth Authentication Interface
OAuthInterface:
  handler: src/modules/oauth/handler/oauth/interface.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-OAuthInterface-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: auth/google
        method: get
        cors: true
    - http:
        path: auth/google/callback
        method: get
        cors: true
    - http:
        path: auth/temp-token/{token}
        method: get
        cors: true

# Contract Domains Management Interface
ContractDomainsInterface:
  handler: src/modules/oauth/handler/contractDomains/interface.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-ContractDomainsInterface-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: oauth/contract-domains
        method: post
        cors: true
    - http: 
        path: oauth/contract-domains/{contractId}
        method: get
        cors: true
    - http:
        path: oauth/contract-domains/{contractId}
        method: put
        cors: true
    - http:
        path: oauth/contract-domains/{contractId}/domains
        method: post
        cors: true