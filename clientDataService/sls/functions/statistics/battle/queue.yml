Resources:
  battleStatisticsEventQueueDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:custom.stage}-battle-statistics-event-handler-dlq
      MessageRetentionPeriod: 1209600 # 14 days 

  battleStatisticsEventQueue:
    Type: AWS::SQS::Queue
    DependsOn: battleStatisticsEventQueueDeadLetterQueue
    Properties:
      QueueName: ${self:service}-${self:custom.stage}-battle-statistics-event-handler-queue
      VisibilityTimeout: 300
      MessageRetentionPeriod: 1209600 # 14 days
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt: [battleStatisticsEventQueueDeadLetterQueue, Arn]
        maxReceiveCount: 3
