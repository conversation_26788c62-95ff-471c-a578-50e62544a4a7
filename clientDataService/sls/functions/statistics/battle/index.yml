getBattleStatistics:
  handler: src/modules/statistics/battle/handler/getBattleStatistics.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "arn:aws:logs:${self:provider.region}:*:*"
    - Effect: Allow
      Action:
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: battleStatistics
        method: get
        cors: true
        authorizer: authorizer

battleStatisticsEventHandler:
  handler: src/modules/statistics/battle/handler/events/index.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: "arn:aws:logs:${self:provider.region}:*:*"
    - Effect: Allow
      Action:
        - sqs:ReceiveMessage
        - sqs:DeleteMessage
        - sqs:GetQueueAttributes
      Resource:
        - Fn::GetAtt: [battleStatisticsEventQueue, Arn]
        - Fn::GetAtt: [battleStatisticsEventQueueDeadLetterQueue, Arn]
  events:
    - sqs:
        arn:
          Fn::GetAtt: [battleStatisticsEventQueue, Arn]
        batchSize: 10
        maximumBatchingWindow: 1
