findSchoolsByContract:
  handler: src/modules/schools/handler/findSchoolsByContract.handler
  events:
    - http:
        path: schools/contract/{contractId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"