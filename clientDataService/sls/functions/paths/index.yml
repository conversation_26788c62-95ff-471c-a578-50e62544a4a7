createPath:
  handler: src/modules/PATHS/handler/createPath.handler
  events:
    - http:
        path: path
        method: post
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"

updatePath:
  handler: src/modules/PATHS/handler/updatePath.handler
  events:
    - http:
        path: path/{pathId}
        method: patch
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"

studentGetSinglePath:
  handler: src/modules/PATHS/handler/studentGetSinglePath.handler
  events:
    - http:
        path: path/{pathId}/student/{studentId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"

userFetchPaths:
  handler: src/modules/PATHS/handler/userFetchPaths.handler
  events:
    - http:
        path: path/user/{userId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"

studentFetchActivity:
  handler: src/modules/PATHS/handler/studentFetchActivity.handler
  events:
    - http:
        path: path/activity/student/{studentId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"

handleStudentStageActivity:
  handler: src/modules/PATHS/handler/handleStudentStageActivity.handler
  events:
    - http:
        path: path/{pathId}/stage/{stageId}/student/{studentId}
        method: post
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*" 

retrieveRewards:
  handler: src/modules/PATHS/handler/retrieveRewards.handler
  events:
    - http:
        path: path/rewards
        method: post
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
        - sqs:SendMessage
      Resource: "*"