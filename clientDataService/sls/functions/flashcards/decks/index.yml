createFlashcardDeck:
  handler: src/modules/flashcards/handler/decks/createSelfDeck.handler
  events:
    - http:
        path: flashcard/deck
        method: post
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

updateDeck:
  handler: src/modules/flashcards/handler/decks/updateDeck.handler
  events:
    - http:
        path: flashcard/deck
        method: patch
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

listSelfDecks:
  handler: src/modules/flashcards/handler/decks/listSelfDecks.handler
  events:
    - http:
        path: flashcard/decks
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

getDeck:
  handler: src/modules/flashcards/handler/decks/getDeck.handler
  events:
    - http:
        path: flashcard/deck/{deckId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

deleteDecks:
  handler: src/modules/flashcards/handler/decks/deleteDecks.handler
  events:
    - http:
        path: flashcard/decks
        method: delete
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"
