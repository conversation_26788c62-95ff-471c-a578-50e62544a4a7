flashcardsInterface:
  handler: src/modules/flashcards/handler/flashcards/interface.handler
  events:
    - http:
        path: flashcard
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: flashcard/self
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: flashcard
        method: patch
        cors: true
        authorizer: authorizer
    - http:
        path: flashcard/batch-answer
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: flashcard
        method: delete
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"