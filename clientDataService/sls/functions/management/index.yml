compareWithDistrict:
  handler: src/modules/management/handler/compareWithDistrict.handler
  events:
    - http:
        path: management/compare/district/{schoolId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

compareSchools:
  handler: src/modules/management/handler/compareSchools.handler
  events:
    - http:
        path: management/compare/schools/{schoolId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*" 