CreateUser:
  handler: src/presentation/handlers/user/create.handler
  # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-CreateUser-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user
        method: post
        cors: true
        # authorizer: authorizer

LoginUser:
  handler: src/presentation/handlers/user/login.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-LoginUser-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/login
        method: post
        cors: true

AddQuestionMetric:
  handler: src/presentation/handlers/user/addQuestionMetric.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddQuestionMetric-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/questionMetric/{id}
        method: post
        cors: true
        authorizer: authorizer

AddLessonMetric:
  handler: src/presentation/handlers/user/addLessonMetric.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddLessonMetric-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/lessonMetric/{id}
        method: post
        cors: true
        authorizer: authorizer

FindUserById:
  handler: src/presentation/handlers/user/findById.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-FindUserById-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/{id}
        method: get
        cors: true
        authorizer: authorizer

FindQuestionMetrics:
  handler: src/presentation/handlers/user/searching/findQuestionMetrics.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-FindQuestionMetrics-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/retrieve/questionMetric/{id}
        method: get
        cors: true
        authorizer: authorizer

FindLessonMetrics:
  handler: src/presentation/handlers/user/searching/findLessonMetrics.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-FindLessonMetrics-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/retrieve/lessonMetric/{id}
        method: get
        cors: true
        authorizer: authorizer

GetSingleLessonWithMetrics:
  handler: src/presentation/handlers/user/getSingleLessonWithMetrics.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetSingleLessonWithMetrics-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/getVideoWithStats/lessonMetric/{id}
        method: get
        cors: true
        authorizer: authorizer

GetGeneralStatisticsForUser:
  handler: src/presentation/handlers/user/getGeneralStatisticsForUser.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetGeneralStatisticsForUser-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/getGeneralStats/{id}
        method: get
        cors: true
        authorizer: authorizer

AddOrRemoveVideoLike:
  handler: src/presentation/handlers/user/addOrRemoveVideoLike.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddOrRemoveVideoLike-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/lessonMetric/like
        method: patch
        cors: true
        authorizer: authorizer

AddVideoWatched:
  handler: src/presentation/handlers/user/addVideoWatched.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddVideoWatched-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
        - sqs:SendMessage 
      Resource: "*"
  events:
    - http:
        path: user/lessonMetric/watched
        method: patch
        cors: true
        authorizer: authorizer

AddOrRemoveVideoSave:
  handler: src/presentation/handlers/user/addOrRemoveVideoSave.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddOrRemoveVideoSave-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/lessonMetric/save
        method: patch
        cors: true
        authorizer: authorizer

AddOrRemoveCommentLike:
  handler: src/presentation/handlers/user/addOrRemoveCommentLike.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddOrRemoveCommentLike-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/commentLikeMetric
        method: patch
        cors: true
        authorizer: authorizer
UpdateUser:
  handler: src/presentation/handlers/user/updateUser.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-UpdateUser-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/{id}
        method: patch
        cors: true
        authorizer: authorizer

DeleteUser:
  handler: src/presentation/handlers/user/delete.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-DeleteUser-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/{id}
        method: delete
        cors: true 
        authorizer: authorizer

AddOrRemoveVideoComment:
  handler: src/presentation/handlers/user/addOrRemoveVideoComment.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddOrRemoveVideoComment-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/videoCommentMetric/{id}
        method: patch
        cors: true 
        authorizer: authorizer

GetCommentsByGroupId:
  handler: src/presentation/handlers/user/getCommentsByGroupId.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetCommentsByGroupId-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/comments/group/{groupId}
        method: get
        cors: true
        authorizer: authorizer

GetCommentsByGroupsBatch:
  handler: src/presentation/handlers/user/getCommentsByGroupsBatch.handler
  # # # layers: ${file(sls/functions/layers.yml):layers}
  memorySize: 1024
  timeout: 30
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetCommentsByGroupsBatch-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/comments/groups/batch
        method: post
        cors: true
        authorizer: authorizer

GetLoginStreakXP:
  handler: src/presentation/handlers/user/getLoginStreakXP.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetLoginStreakXP-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
        - sqs:SendMessage 
      Resource: "*"
  events:
    - http:
        path: user/loginStreak
        method: post
        cors: true 
        authorizer: authorizer
