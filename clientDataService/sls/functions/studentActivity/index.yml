trackLoginActivity:
  handler: src/modules/studentActivityTracker/handler/trackLoginActivity.handler
  events:
    - http:
        path: activity/login/{userId}
        method: post
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

fetchLessonMetrics:
  handler: src/modules/studentActivityTracker/handler/fetchLessonMetrics.handler
  events:
    - http:
        path: activity/lessons/{userId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

fetchQuestionMetrics:
  handler: src/modules/studentActivityTracker/handler/fetchQuestionMetrics.handler
  events:
    - http:
        path: activity/questions/{userId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"