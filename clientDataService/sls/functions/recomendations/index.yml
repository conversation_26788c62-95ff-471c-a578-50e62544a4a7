# generateRecommendations:
#   handler: src/modules/recomendations/handler/generateRecommendations.handler
#   events:
#     - http:
#         path: recommendations/generate/student/{studentId}
#         method: post
#         cors: true
#   memorySize: 1024
#   maximumRetryAttempts: 0
#   iamRoleStatements:
#     - Effect: Allow
#       Action:
#         - logs:CreateLogGroup
#         - logs:CreateLogStream
#         - logs:PutLogEvents
#         - execute-api:Invoke
#       Resource: "*"

# getStudentRecommendations:
#   handler: src/modules/recomendations/handler/getStudentRecommendations.handler
#   events:
#     - http:
#         path: recommendations/student/{studentId}
#         method: get
#         cors: true
#   memorySize: 1024
#   maximumRetryAttempts: 0
#   iamRoleStatements:
#     - Effect: Allow
#       Action:
#         - logs:CreateLogGroup
#         - logs:CreateLogStream
#         - logs:PutLogEvents
#         - execute-api:Invoke
#       Resource: "*"

# getRecommendationsByType:
#   handler: src/modules/recomendations/handler/getRecommendationsByType.handler
#   events:
#     - http:
#         path: recommendations/type/{type}/student/{studentId}
#         method: get
#         cors: true
#   memorySize: 1024
#   maximumRetryAttempts: 0
#   iamRoleStatements:
#     - Effect: Allow
#       Action:
#         - logs:CreateLogGroup
#         - logs:CreateLogStream
#         - logs:PutLogEvents
#         - execute-api:Invoke
#       Resource: "*"

# updateRecommendationStatus:
#   handler: src/modules/recomendations/handler/updateRecommendationStatus.handler
#   events:
#     - http:
#         path: recommendations/{recommendationId}/status
#         method: patch
#         cors: true
#   memorySize: 1024
#   maximumRetryAttempts: 0
#   iamRoleStatements:
#     - Effect: Allow
#       Action:
#         - logs:CreateLogGroup
#         - logs:CreateLogStream
#         - logs:PutLogEvents
#         - execute-api:Invoke
#       Resource: "*" 