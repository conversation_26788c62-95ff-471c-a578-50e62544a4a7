Resources:
  shopEventQueueDeadLetterQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: ${self:service}-${self:custom.stage}-shop-event-handler-dlq
      MessageRetentionPeriod: 1209600 # 14 days 

  shopEventQueue:
    Type: AWS::SQS::Queue
    DependsOn: shopEventQueueDeadLetterQueue
    Properties:
      QueueName: ${self:service}-${self:custom.stage}-shop-event-handler-queue
      VisibilityTimeout: 300
      MessageRetentionPeriod: 1209600 # 14 days
      RedrivePolicy:
        deadLetterTargetArn:
          Fn::GetAtt: [shopEventQueueDeadLetterQueue, Arn]
        maxReceiveCount: 3
