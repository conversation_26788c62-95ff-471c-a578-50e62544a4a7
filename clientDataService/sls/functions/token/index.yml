validateToken:
  handler: src/presentation/handlers/middleware/validateToken.handler
  events:
    - http:
        path: token/validate
        method: get
        cors: true
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"
