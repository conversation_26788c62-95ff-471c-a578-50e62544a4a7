CreateSchool:
  handler: src/presentation/handlers/school/createSchool.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-CreateSchool-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: school/new
        method: post
        cors: true
        # authorizer: authorizer

UpdateSchool:
  handler: src/presentation/handlers/school/updateSchool.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-UpdateSchool-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: school/update/{schoolId}
        method: patch
        cors: true
        authorizer: authorizer 