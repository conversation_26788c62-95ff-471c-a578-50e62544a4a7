ClusterByOlympiad:
  handler: src/presentation/handlers/questionMetrics/clusterByOlympiad.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-ClusterByOlympiad-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: user/cluster/olympiads/questionMetric/{id}
        method: get
        cors: true
        authorizer: authorizer 