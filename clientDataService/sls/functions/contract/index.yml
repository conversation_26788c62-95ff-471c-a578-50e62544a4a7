createContract:
  handler: src/modules/contract/handler/createContract.handler
  events:
    - http:
        path: contract
        method: post
        cors: true
        # authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

updateContract:
  handler: src/modules/contract/handler/updateContract.handler
  events:
    - http:
        path: contract/{contractId}
        method: patch
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

deactivateContract:
  handler: src/modules/contract/handler/deactivateContract.handler
  events:
    - http:
        path: contract/deactivate/{contractId}
        method: patch
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

getContractActivitySummary:
  handler: src/modules/contract/handler/getContractActivitySummary.handler
  events:
    - http:
        path: contract/activity/{contractId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*"

listUsersByContract:
  handler: src/modules/contract/handler/listUsersByContract.handler
  events:
    - http:
        path: contract/users/{contractId}
        method: get
        cors: true
        authorizer: authorizer
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
      Resource: "*" 