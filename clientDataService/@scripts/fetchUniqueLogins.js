import mongoose from 'mongoose';
import fs from 'fs';
import path from 'path';
import { LoginTimeSeriesModel } from '../src/domain/models/tracking/loginTimeSeries.js';
import { UserModel } from '../src/domain/models/userModel.js';

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    // You may need to update this connection string based on your environment
    await mongoose.connect('url', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

// Read configuration from JSON file
function readConfig() {
  try {
    const configPath = path.resolve('../../@mocks/login.json');
    const configData = fs.readFileSync(configPath, 'utf8');
    return JSON.parse(configData);
  } catch (error) {
    console.error('Error reading configuration:', error);
    return { hoursToLookBack: 24 }; // Default to 24 hours if config file not found
  }
}

// Format date for Brazil timezone (UTC-3)
function formatDateForBrazil(dateString) {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  // Subtract 3 hours for Brazil timezone
  date.setHours(date.getHours() - 3);
  
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${day}/${month}/${year} ${hours}:${minutes}`;
}

// Find peak login hour
function findPeakLoginHour(loginTimestamps) {
  // Adjust all timestamps to Brazil time (UTC-3)
  const adjustedTimestamps = loginTimestamps.map(login => {
    const timestamp = new Date(login.timestamp);
    timestamp.setHours(timestamp.getHours() - 3);
    return timestamp;
  });
  
  // Group logins by hour
  const loginsByHour = {};
  
  adjustedTimestamps.forEach(timestamp => {
    // Create hour key in format HH:00
    const hourKey = `${String(timestamp.getHours()).padStart(2, '0')}:00`;
    if (!loginsByHour[hourKey]) {
      loginsByHour[hourKey] = 0;
    }
    loginsByHour[hourKey]++;
  });
  
  // Find the hour with the most logins
  let peakHour = '';
  let peakCount = 0;
  
  for (const [hour, count] of Object.entries(loginsByHour)) {
    if (count > peakCount) {
      peakCount = count;
      peakHour = hour;
    }
  }
  
  // Format the peak hour range (X:XX - X+1:XX)
  if (peakHour) {
    const hourValue = parseInt(peakHour.split(':')[0], 10);
    const nextHour = (hourValue + 1) % 24;
    const peakRange = `${peakHour} - ${String(nextHour).padStart(2, '0')}:00`;
    return { peakRange, peakCount };
  }
  
  return { peakRange: 'N/A', peakCount: 0 };
}

// Fetch unique logins from the last X hours
async function fetchUniqueLogins() {
  const config = readConfig();
  const hoursToLookBack = config.hoursToLookBack || 24;
  
  const startTime = new Date();
  startTime.setHours(startTime.getHours() - hoursToLookBack);
  
  console.log(`Fetching unique logins from the last ${hoursToLookBack} hours (since ${startTime.toISOString()})`);
  
  try {
    // Find login timestamps within the time range
    const loginTimestamps = await LoginTimeSeriesModel.find({
      timestamp: { $gte: startTime }
    }).sort({ timestamp: -1 });
    
    console.log(`Found ${loginTimestamps.length} login events`);
    
    // Find peak login hour
    const { peakRange, peakCount } = findPeakLoginHour(loginTimestamps);
    
    // Extract unique user IDs from metadata
    const uniqueUserIds = [...new Set(loginTimestamps.map(login => 
      login.metadata && login.metadata.userId ? login.metadata.userId : null
    ).filter(id => id !== null)) ];
    
    console.log(`Found ${uniqueUserIds.length} unique users who logged in`);
    
    // Fetch user details for the unique user IDs
    const users = await UserModel.find({
      _id: { $in: uniqueUserIds }
    }).select('name');
    
    // Create a mapping of user IDs to their login timestamps
    const userLoginMap = {};
    loginTimestamps.forEach(login => {
      if (login.metadata && login.metadata.userId) {
        const userId = login.metadata.userId.toString();
        if (!userLoginMap[userId] || login.timestamp > userLoginMap[userId]) {
          userLoginMap[userId] = login.timestamp;
        }
      }
    });
    
    // Filter out users with names like "ALUNO X" where X is a number (case insensitive)
    const filteredUsers = users.filter(user => {
      // Skip users with no name
      if (!user.name) return true;
      
      // Check if the name matches the pattern "ALUNO X" where X is a number
      const pattern = /^aluno\s+\d+$/i;
      return !pattern.test(user.name.trim());
    });
    
    console.log(`Filtered out ${users.length - filteredUsers.length} test users with names matching "ALUNO X" pattern`);
    
    // Create a report with simplified user info and their last login time
    const uniqueLoginsReport = filteredUsers.map(user => {
      const userId = user._id.toString();
      const lastLogin = userLoginMap[userId];
      
      return {
        name: user.name,
        lastLogin: lastLogin ? formatDateForBrazil(lastLogin) : ''
      };
    });
    
    // Sort by last login time (most recent first)
    uniqueLoginsReport.sort((a, b) => {
      if (!a.lastLogin) return 1;
      if (!b.lastLogin) return -1;
      
      // Parse the formatted dates back for comparison
      const partsA = a.lastLogin.split(' ');
      const partsB = b.lastLogin.split(' ');
      
      const dateA = partsA[0].split('/').reverse().join('-') + 'T' + partsA[1];
      const dateB = partsB[0].split('/').reverse().join('-') + 'T' + partsB[1];
      
      return new Date(dateB) - new Date(dateA);
    });
    
    // Save the report to a CSV file with fewer columns
    const csvData = [
      'Name,Last Login',
      ...uniqueLoginsReport.map(u => `"${u.name}","${u.lastLogin}"`),
      '',
      `Total de logins únicos,${filteredUsers.length}`,
      `Horário de pico de logins,${peakRange} (${peakCount} logins)`
    ].join('\n');
    
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
    const outputPath = path.resolve(`./uniqueLogins_${timestamp}.csv`);
    fs.writeFileSync(outputPath, csvData);
    
    console.log(`Report saved to ${outputPath}`);
    console.log(`Peak login hour: ${peakRange} with ${peakCount} logins`);
    
    return { 
      uniqueLogins: uniqueLoginsReport, 
      totalUniqueLogins: filteredUsers.length,
      peakLoginHour: peakRange,
      peakLoginCount: peakCount
    };
    
  } catch (error) {
    console.error('Error fetching login data:', error);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    await connectToMongoDB();
    const uniqueLogins = await fetchUniqueLogins();
    
    console.log('\nUnique Logins Summary:');
    console.log(`Total unique users logged in: ${uniqueLogins.totalUniqueLogins}`);
    
    // Count by role
    const roleCount = uniqueLogins.uniqueLogins.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {});
    
    console.log('\nBreakdown by role:');
    Object.entries(roleCount).forEach(([role, count]) => {
      console.log(`${role}: ${count} users`);
    });
    
    mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
    
  } catch (error) {
    console.error('Script execution failed:', error);
    mongoose.disconnect();
    process.exit(1);
  }
}

main(); 