import csv from 'csv-parser';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
dotenv.config();
import { fileURLToPath } from 'url';
import mongoose from 'mongoose';
import createUserService from '../src/application/services/user/create.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// MongoDB connection setup
async function connectToDatabase() {
    try {
        const connectionString = process.env.CONNECTION_STRING;
        if (!connectionString) {
            throw new Error('CONNECTION_STRING environment variable is not defined');
        }
        
        await mongoose.connect(connectionString);
        console.log('Connected to MongoDB');
    } catch (error) {
        console.error('Failed to connect to MongoDB:', error);
        process.exit(1);
    }
}

async function createStudentsFromCSV(csvFilePath, schoolId, separator='\t', batchSize = 5) {
    const results = [];
    
    // Read and parse CSV
    await new Promise((resolve, reject) => {
        fs.createReadStream(csvFilePath)
            .pipe(csv({
                headers: ['Nome', 'Username', 'Email', 'Email Formatado', 'Senha', 'Escola', 'Ano', 'Código', 'Ativo'],
                separator: separator
            }))
            .on('data', (data) => results.push(data))
            .on('end', resolve)
            .on('error', reject);
    });

    console.log(`Found ${results.length} students to create in school: ${results[0].Escola}`);
    console.log(`Processing in batches of ${batchSize} students to reduce database load`);

    // Transform all rows into userData objects
    const studentsToCreate = results.map((row, index) => {
        const userData = {
            name: row.Nome,           // 1st column
            username: row.Username,   // 2nd column
            email: row.Email,         // 3rd column
            password: row.Senha,      // 5th column
            school: row.Escola,       // 6th column
            role: 'student',
            enrollmentId: row['Código'], // 8th column
            schoolId: schoolId,
            CPF: '',
        };
        return userData;
    });

    try {
        // Create students using the application service
        const createdStudents = [];
        const failedStudents = [];
        
        // Process students in batches
        for (let batchStart = 0; batchStart < studentsToCreate.length; batchStart += batchSize) {
            const batch = studentsToCreate.slice(batchStart, batchStart + batchSize);
            console.log(`\n--- Processing batch ${Math.floor(batchStart / batchSize) + 1}/${Math.ceil(studentsToCreate.length / batchSize)} ---`);
            
            for (let i = 0; i < batch.length; i++) {
                const studentData = batch[i];
                const globalIndex = batchStart + i;
                let retryCount = 0;
                const maxRetries = 3;
                let success = false;
                
                while (retryCount < maxRetries && !success) {
                    try {
                        console.log(`Creating student ${globalIndex + 1}/${studentsToCreate.length}: ${studentData.email} (attempt ${retryCount + 1})`);
                        
                        const student = await createUserService(studentData);
                        console.log("student", student)
                        createdStudents.push(student);
                        console.log(`✓ Successfully created student: ${studentData.email}`);
                        success = true;
                        
                    } catch (error) {
                        retryCount++;
                        
                        // Check if it's a lock timeout error
                        if (error.message.includes('LockTimeout') || error.message.includes('TransientTransactionError')) {
                            if (retryCount < maxRetries) {
                                const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
                                console.log(`⚠ Lock timeout for ${studentData.email}, retrying in ${delay}ms...`);
                                await new Promise(resolve => setTimeout(resolve, delay));
                                continue;
                            }
                        }
                        
                        console.error(`Failed to create student ${studentData.email}:`, error.message);
                        failedStudents.push({
                            email: studentData.email,
                            error: error.message
                        });
                        break;
                    }
                }
                
                // Add a small delay between users to reduce lock contention
                if (i < batch.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
            }
            
            // Add a longer delay between batches
            if (batchStart + batchSize < studentsToCreate.length) {
                console.log(`Batch completed. Waiting 2 seconds before next batch...`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        console.log(`\n=== MIGRATION SUMMARY ===`);
        console.log(`Successfully created: ${createdStudents.length} students`);
        console.log(`Failed to create: ${failedStudents.length} students`);
        
        if (failedStudents.length > 0) {
            console.log(`\nFailed students:`);
            failedStudents.forEach(failed => {
                console.log(`- ${failed.email}: ${failed.error}`);
            });
        }
        
        return {
            created: createdStudents,
            failed: failedStudents
        };
        
    } catch (error) {
        console.error('Failed to create students:', error.message);
        throw error;
    }
}

// Load configuration from command line arguments or mock file
async function loadConfig() {
    const args = process.argv.slice(2);
    
    if (args.length >= 1) {
        return {
            csvPath: args[0], // Use the first argument as the CSV file path
            schoolId: args[1] // Use second argument as schoolId or default
        };
    }

    try {
        const mockPath = path.join(__dirname, '..', '@mocks', 'users.json');
        const mockConfig = JSON.parse(fs.readFileSync(mockPath, 'utf8'));
        return mockConfig;
    } catch (error) {
        console.error('Error loading mock configuration:', error);
        process.exit(1);
    }
}

// Main execution
async function main() {
    try {
        await connectToDatabase();
        
        const config = await loadConfig();
        if (!config.csvPath || !config.schoolId) {
            throw new Error('Missing required configuration: csvPath and schoolId');
        }
        
        console.log(`Starting student creation from CSV: ${config.csvPath}`);
        console.log(`School ID: ${config.schoolId}`);
        
        const result = await createStudentsFromCSV(config.csvPath, config.schoolId);
        
        console.log('\nStudent creation completed successfully!');
        
    } catch (error) {
        console.error('Student creation failed:', error);
        process.exit(1);
    } finally {
        await mongoose.connection.close();
        console.log('Database connection closed');
    }
}

// Run the script
main();
