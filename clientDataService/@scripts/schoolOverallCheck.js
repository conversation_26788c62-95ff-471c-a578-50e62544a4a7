import mongoose from 'mongoose';
import fs from 'fs';
import path from 'path';
import { UserModel } from '../src/domain/models/userModel.js';
import { SchoolModel } from '../src/domain/models/school/schoolModel.js';
import BalanceAccountModel from '../src/domain/models/balanceAccountModel.js';
import { QuestionMetricModel } from '../src/domain/models/metrics/questionMetricsModel.js';
import { LessonMetricModel } from '../src/domain/models/metrics/lessonMetricsModel.js';
import { TrackingModel } from '../src/domain/models/tracking/trackingModel.js';
import { ContractModel } from '../src/domain/models/contract/contractModel.js';
import dotenv from 'dotenv';

dotenv.config();

const CONNECTION_STRING = process.env.CONNECTION_STRING;

// Connect to MongoDB
async function connectToMongoDB(mongodbUrl) {
  console.log("mongodbUrl: ", mongodbUrl)
  try {
    await mongoose.connect(mongodbUrl, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

// Filter test users (Pattern: ALUNO X)
function filterTestUsers(users) {
  const filtered = users.filter(user => {
    if (!user.name) return true;
    
    const pattern = /^aluno\s+\d+$/i;
    return !pattern.test(user.name.trim());
  });
  
  console.log(`Filtered ${users.length - filtered.length} test users with names matching "ALUNO X" pattern`);
  return filtered;
}

// Get date range for analysis (last 30 days by default)
function getDateRange(daysAgo = 30) {
  const endDate = new Date();
  endDate.setHours(23, 59, 59, 999);
  
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - daysAgo);
  startDate.setHours(0, 0, 0, 0);
  
  return { startDate, endDate };
}

// Format date to Brazilian format (UTC-3)
function formatDateBR(dateString) {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  date.setHours(date.getHours() - 3);
  
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${day}/${month}/${year} ${hours}:${minutes}`;
}

// Generate comprehensive school check
async function generateSchoolOverallCheck(schoolId) {
  try {
    console.log(`\n=== GENERATING OVERALL CHECK FOR SCHOOL ID: ${schoolId} ===`);
    
    // 1. Get school information
    const school = await SchoolModel.findById(schoolId);
    if (!school) {
      throw new Error(`School with ID ${schoolId} not found`);
    }
    
    console.log(`\nSchool: ${school.name}`);
    console.log(`Address: ${school.address}`);
    console.log(`Status: ${school.inactive ? 'Inactive' : 'Active'}`);
    console.log(`Created: ${formatDateBR(school.createdAt)}`);
    
    // 2. Get contract information
    const contract = await ContractModel.findById(school.contractId);
    const contractInfo = contract ? {
      name: contract.name,
      description: contract.description,
      maxProfiles: contract.maxNumberOfProfiles,
      price: contract.price,
      isGov: contract.clientInfo?.isGov || false,
      dateOfContract: formatDateBR(contract.dateOfContract),
      inactive: contract.inactive
    } : null;
    
    // 3. Get all students from this school
    const allStudents = await UserModel.find({
      schoolId: school._id,
      role: 'student'
    }).populate('balance');
    
    console.log(`\nTotal students found: ${allStudents.length}`);
    
    // Filter test users
    const realStudents = filterTestUsers(allStudents);
    console.log(`Real students (after filtering): ${realStudents.length}`);
    
    if (realStudents.length === 0) {
      console.log('No real students found in this school');
      return {
        school: school.toObject(),
        contract: contractInfo,
        students: {
          total: 0,
          real: 0,
          test: 0,
          schoolYearDistribution: {}
        },
        metrics: {
          totalXP: 0,
          averageXP: 0,
          totalAtom: 0,
          averageAtom: 0,
          topStudent: null,
          bottomStudent: null
        },
        activity: {
          questionsAnswered: 0,
          lessonsWatched: 0,
          activeStudents: 0,
          lastActivity: null,
          recentActivity: {
            questionsLast7Days: 0,
            lessonsLast7Days: 0
          }
        },
        performance: {
          correctAnswers: 0,
          totalAnswers: 0,
          accuracyRate: 0,
          subjects: {}
        },
        ranking: {
          allStudents: []
        }
      };
    }
    
    // 4. Get student balances
    const studentIds = realStudents.map(s => s._id);
    const balances = await BalanceAccountModel.find({
      ownerId: { $in: studentIds }
    });
    
    // Map students with their balances
    const studentsWithBalance = realStudents.map(student => {
      const balance = balances.find(b => b.ownerId.toString() === student._id.toString());
      return {
        ...student.toObject(),
        xp: balance ? balance.xp : 0,
        atom: balance ? balance.atom : 0
      };
    });
    
    // 5. Calculate basic metrics
    const totalXP = studentsWithBalance.reduce((sum, s) => sum + s.xp, 0);
    const averageXP = totalXP / studentsWithBalance.length;
    const totalAtom = studentsWithBalance.reduce((sum, s) => sum + s.atom, 0);
    const averageAtom = totalAtom / studentsWithBalance.length;
    
    // Top and bottom students
    const sortedByXP = studentsWithBalance.sort((a, b) => b.xp - a.xp);
    const topStudent = sortedByXP[0];
    const bottomStudent = sortedByXP[sortedByXP.length - 1];
    
    // 6. Get activity data (all time)
    // Questions answered (all time)
    const questionMetrics = await QuestionMetricModel.find({
      userId: { $in: studentIds }
    });
    
    // Lessons watched (all time)
    const lessonMetrics = await LessonMetricModel.find({
      userId: { $in: studentIds }
    });
    
    // Active students (with any activity all time)
    const activeStudentIds = new Set([
      ...questionMetrics.map(q => q.userId.toString()),
      ...lessonMetrics.map(l => l.userId.toString())
    ]);
    
    // Last activity
    const lastActivity = await TrackingModel.findOne({
      userId: { $in: studentIds }
    }).sort({ updatedAt: -1 });
    
    // 7. Performance analysis (all time)
    const correctAnswers = questionMetrics.filter(q => q.isCorrect).length;
    const totalAnswers = questionMetrics.length;
    const accuracyRate = totalAnswers > 0 ? (correctAnswers / totalAnswers) * 100 : 0;
    
    // Subject breakdown (all time)
    const subjects = {};
    questionMetrics.forEach(q => {
      if (!subjects[q.subject]) {
        subjects[q.subject] = { total: 0, correct: 0 };
      }
      subjects[q.subject].total++;
      if (q.isCorrect) subjects[q.subject].correct++;
    });
    
    // Calculate accuracy per subject
    Object.keys(subjects).forEach(subject => {
      subjects[subject].accuracy = subjects[subject].total > 0 
        ? (subjects[subject].correct / subjects[subject].total) * 100 
        : 0;
    });
    
    // 8. School year distribution
    const schoolYearDistribution = {};
    studentsWithBalance.forEach(student => {
      const year = student.schoolYear || 'Unknown';
      schoolYearDistribution[year] = (schoolYearDistribution[year] || 0) + 1;
    });
    
    // 9. Recent activity (last 7 days for comparison)
    const { startDate: weekStart, endDate: weekEnd } = getDateRange(7);
    const recentQuestions = await QuestionMetricModel.find({
      userId: { $in: studentIds },
      createdAt: { $gte: weekStart, $lte: weekEnd }
    });
    
    const recentLessons = await LessonMetricModel.find({
      userId: { $in: studentIds },
      createdAt: { $gte: weekStart, $lte: weekEnd }
    });
    
    // 10. Calculate individual student activity
    const studentActivityMap = {};
    
    // Count questions per student
    questionMetrics.forEach(q => {
      const userId = q.userId.toString();
      if (!studentActivityMap[userId]) {
        studentActivityMap[userId] = { questionsAnswered: 0, lessonsWatched: 0 };
      }
      studentActivityMap[userId].questionsAnswered++;
    });
    
    // Count lessons per student
    lessonMetrics.forEach(l => {
      const userId = l.userId.toString();
      if (!studentActivityMap[userId]) {
        studentActivityMap[userId] = { questionsAnswered: 0, lessonsWatched: 0 };
      }
      studentActivityMap[userId].lessonsWatched++;
    });
    
    // 11. Compile results
    const results = {
      school: {
        id: school._id.toString(),
        name: school.name,
        address: school.address,
        coordinates: school.coordinates,
        inactive: school.inactive,
        createdAt: formatDateBR(school.createdAt),
        updatedAt: formatDateBR(school.updatedAt)
      },
      contract: contractInfo,
      students: {
        total: allStudents.length,
        real: realStudents.length,
        test: allStudents.length - realStudents.length,
        schoolYearDistribution
      },
      metrics: {
        totalXP,
        averageXP: Math.round(averageXP * 100) / 100,
        totalAtom,
        averageAtom: Math.round(averageAtom * 100) / 100,
        topStudent: topStudent ? {
          name: topStudent.name,
          email: topStudent.email,
          username: topStudent.username,
          schoolYear: topStudent.schoolYear,
          enrollmentId: topStudent.enrollmentId,
          xp: topStudent.xp,
          atom: topStudent.atom
        } : null,
        bottomStudent: bottomStudent ? {
          name: bottomStudent.name,
          email: bottomStudent.email,
          username: bottomStudent.username,
          schoolYear: bottomStudent.schoolYear,
          enrollmentId: bottomStudent.enrollmentId,
          xp: bottomStudent.xp,
          atom: bottomStudent.atom
        } : null
      },
      activity: {
        questionsAnswered: questionMetrics.length,
        lessonsWatched: lessonMetrics.length,
        activeStudents: activeStudentIds.size,
        lastActivity: lastActivity ? formatDateBR(lastActivity.updatedAt) : null,
        recentActivity: {
          questionsLast7Days: recentQuestions.length,
          lessonsLast7Days: recentLessons.length
        }
      },
      performance: {
        correctAnswers,
        totalAnswers,
        accuracyRate: Math.round(accuracyRate * 100) / 100,
        subjects
      },
      ranking: {
        allStudents: sortedByXP.map((student, index) => {
          const activity = studentActivityMap[student._id.toString()] || { questionsAnswered: 0, lessonsWatched: 0 };
          return {
            position: index + 1,
            name: student.name,
            email: student.email,
            schoolYear: student.schoolYear,
            xp: student.xp,
            atom: student.atom,
            questionsAnswered: activity.questionsAnswered,
            lessonsWatched: activity.lessonsWatched
          };
        })
      }
    };
    
    return results;
    
  } catch (error) {
    console.error('Error generating school overall check:', error);
    throw error;
  }
}

// Generate CSV report
function generateCSVReport(results) {
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
  
  // Main report
  const mainReport = [
    'SCHOOL OVERALL CHECK REPORT',
    `Generated: ${new Date().toLocaleString('pt-BR')}`,
    '',
    'SCHOOL INFORMATION',
    `Name,${results.school.name}`,
    `Address,${results.school.address}`,
    `Status,${results.school.inactive ? 'Inactive' : 'Active'}`,
    `Created,${results.school.createdAt}`,
    '',
    'CONTRACT INFORMATION',
    ...(results.contract ? [
      `Name,${results.contract.name}`,
      `Description,${results.contract.description || 'N/A'}`,
      `Max Profiles,${results.contract.maxProfiles}`,
      `Price,${results.contract.price}`,
      `Government Client,${results.contract.isGov ? 'Yes' : 'No'}`,
      `Contract Date,${results.contract.dateOfContract}`,
      `Status,${results.contract.inactive ? 'Inactive' : 'Active'}`
    ] : ['No contract information available']),
    '',
    'STUDENT STATISTICS',
    `Total Students,${results.students.total}`,
    `Real Students,${results.students.real}`,
    `Test Students,${results.students.test}`,
    '',
    'SCHOOL YEAR DISTRIBUTION',
    'Year,Count',
    ...Object.entries(results.students.schoolYearDistribution).map(([year, count]) => `${year},${count}`),
    '',
    'PERFORMANCE METRICS',
    `Total XP,${results.metrics.totalXP}`,
    `Average XP,${results.metrics.averageXP}`,
    `Total Atom,${results.metrics.totalAtom}`,
    `Average Atom,${results.metrics.averageAtom}`,
    '',
    'TOP STUDENT',
    ...(results.metrics.topStudent ? [
      `Name,${results.metrics.topStudent.name}`,
      `Email,${results.metrics.topStudent.email}`,
      `School Year,${results.metrics.topStudent.schoolYear || 'N/A'}`,
      `XP,${results.metrics.topStudent.xp}`,
      `Atom,${results.metrics.topStudent.atom}`
    ] : ['No top student data']),
    '',
    'ACTIVITY DATA (All Time)',
    `Questions Answered,${results.activity.questionsAnswered}`,
    `Lessons Watched,${results.activity.lessonsWatched}`,
    `Active Students,${results.activity.activeStudents}`,
    `Last Activity,${results.activity.lastActivity || 'N/A'}`,
    '',
    'RECENT ACTIVITY (Last 7 Days)',
    `Questions,${results.activity.recentActivity.questionsLast7Days}`,
    `Lessons,${results.activity.recentActivity.lessonsLast7Days}`,
    '',
    'PERFORMANCE ANALYSIS (All Time)',
    `Correct Answers,${results.performance.correctAnswers}`,
    `Total Answers,${results.performance.totalAnswers}`,
    `Accuracy Rate,${results.performance.accuracyRate}%`,
    '',
    'SUBJECT PERFORMANCE (All Time)',
    'Subject,Total Questions,Correct Answers,Accuracy Rate',
    ...Object.entries(results.performance.subjects).map(([subject, data]) => 
      `${subject},${data.total},${data.correct},${Math.round(data.accuracy * 100) / 100}%`
    ),
    '',
    'ALL STUDENTS RANKING (All Time)',
    'Position,Name,Email,School Year,XP,Atom,Questions Answered,Lessons Watched',
    ...results.ranking.allStudents.map(student => 
      `${student.position},"${student.name}","${student.email}",${student.schoolYear || 'N/A'},${student.xp},${student.atom},${student.questionsAnswered},${student.lessonsWatched}`
    )
  ].join('\n');
  
  return {
    content: mainReport,
    filename: `school_overall_check_${results.school.name.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.csv`
  };
}

// Main execution
async function main() {
  try {
    console.log("CONNECTION_STRING: ", CONNECTION_STRING)
    // Get school ID from command line arguments or use default
    const schoolId = '67d64645b3ffed181377e856';
    
    await connectToMongoDB(CONNECTION_STRING);
    
    console.log(`Starting overall check for school ID: ${schoolId}`);
    
    const results = await generateSchoolOverallCheck(schoolId);
    
    // Generate and save CSV report
    const csvReport = generateCSVReport(results);
    const filePath = path.resolve(csvReport.filename);
    fs.writeFileSync(filePath, csvReport.content);
    
    // Console output
    console.log('\n' + '='.repeat(60));
    console.log('SCHOOL OVERALL CHECK RESULTS');
    console.log('='.repeat(60));
    
    console.log(`\n📚 SCHOOL: ${results.school.name}`);
    console.log(`📍 Address: ${results.school.address}`);
    console.log(`📅 Created: ${results.school.createdAt}`);
    console.log(`🔗 Status: ${results.school.inactive ? '❌ Inactive' : '✅ Active'}`);
    
    if (results.contract) {
      console.log(`\n📋 CONTRACT: ${results.contract.name}`);
      console.log(`💰 Price: $${results.contract.price}`);
      console.log(`👥 Max Profiles: ${results.contract.maxProfiles}`);
      console.log(`🏛️ Government: ${results.contract.isGov ? 'Yes' : 'No'}`);
    }
    
    console.log(`\n👥 STUDENTS:`);
    console.log(`   Total: ${results.students.total}`);
    console.log(`   Real: ${results.students.real}`);
    console.log(`   Test: ${results.students.test}`);
    
    console.log(`\n📊 PERFORMANCE:`);
    console.log(`   Total XP: ${results.metrics.totalXP.toLocaleString()}`);
    console.log(`   Average XP: ${results.metrics.averageXP.toLocaleString()}`);
    console.log(`   Total Atom: ${results.metrics.totalAtom.toLocaleString()}`);
    console.log(`   Average Atom: ${results.metrics.averageAtom.toLocaleString()}`);
    
    if (results.metrics.topStudent) {
      console.log(`\n🏆 TOP STUDENT:`);
      console.log(`   Name: ${results.metrics.topStudent.name}`);
      console.log(`   XP: ${results.metrics.topStudent.xp.toLocaleString()}`);
      console.log(`   Atom: ${results.metrics.topStudent.atom.toLocaleString()}`);
    }
    
    console.log(`\n📈 ACTIVITY (All Time):`);
    console.log(`   Questions: ${results.activity.questionsAnswered}`);
    console.log(`   Lessons: ${results.activity.lessonsWatched}`);
    console.log(`   Active Students: ${results.activity.activeStudents}`);
    
    console.log(`\n🎯 PERFORMANCE (All Time):`);
    console.log(`   Accuracy: ${results.performance.accuracyRate}%`);
    console.log(`   Questions: ${results.performance.totalAnswers}`);
    console.log(`   Correct: ${results.performance.correctAnswers}`);
    
    console.log(`\n📊 STUDENT RANKING:`);
    console.log(`   Total Students Listed: ${results.ranking.allStudents.length}`);
    console.log(`   Top 5 Students:`);
    results.ranking.allStudents.slice(0, 5).forEach((student, index) => {
      console.log(`   ${index + 1}. ${student.name} - XP: ${student.xp.toLocaleString()}, Questions: ${student.questionsAnswered}, Lessons: ${student.lessonsWatched}`);
    });
    
    console.log(`\n📁 Report saved to: ${filePath}`);
    
    await mongoose.disconnect();
    return true;
    
  } catch (error) {
    console.error('Error in execution:', error);
    await mongoose.disconnect();
    process.exit(1);
  }
}

main();
