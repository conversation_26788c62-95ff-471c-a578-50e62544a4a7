import mongoose from 'mongoose';
import fs from 'fs';
import path from 'path';
import { UserModel } from '../src/domain/models/userModel.js';
import { SchoolModel } from '../src/domain/models/school/schoolModel.js';
import BalanceAccountModel from '../src/domain/models/balanceAccountModel.js';
import dotenv from 'dotenv';

dotenv.config();

const CONNECTION_STRING = process.env.CONNECTION_STRING;

// Conecta ao MongoDB
async function conectarAoMongoDB(mongodbUrl) {
  try {
    await mongoose.connect(mongodbUrl, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Conectado ao MongoDB');
  } catch (error) {
    console.error('Falha ao conectar ao MongoDB:', error);
    process.exit(1);
  }
}

// Read students from JSON file
function readStudentsFromJSON() {
  try {
    const studentsPath = path.resolve('@scripts/teachers/students.json');
    const studentsData = fs.readFileSync(studentsPath, 'utf8');
    const students = JSON.parse(studentsData);
    console.log(`📖 Read ${students.length} students from JSON file`);
    return students;
  } catch (error) {
    console.error('❌ Error reading students JSON file:', error);
    throw error;
  }
}

// Find users by email and update their balance accounts
async function giveAtomsToUsers(students, atomsToGive = 2500) {
  const results = {
    totalStudents: students.length,
    foundUsers: 0,
    updatedBalances: 0,
    createdBalances: 0,
    errors: [],
    success: []
  };

  console.log(`\n🎯 Starting to give ${atomsToGive} atoms to ${students.length} students...`);

  for (const student of students) {
    try {
      console.log(`\n🔍 Processing: ${student.name} (${student.email})`);
      
      // Find user by email
      const user = await UserModel.findOne({ email: student.email });
      
      if (!user) {
        console.log(`❌ User not found: ${student.email}`);
        results.errors.push({
          email: student.email,
          name: student.name,
          error: 'User not found'
        });
        continue;
      }

      results.foundUsers++;
      console.log(`✅ User found: ${user.name} (ID: ${user._id})`);

      // Find balance account by ownerId
      let balanceAccount = await BalanceAccountModel.findOne({ 
        ownerId: user._id.toString() 
      });

      if (!balanceAccount) {
        // Create new balance account if it doesn't exist
        console.log(`📝 Creating new balance account for user: ${user.name}`);
        balanceAccount = new BalanceAccountModel({
          username: user.username || user.name,
          xp: 0,
          atom: atomsToGive,
          ownerId: user._id.toString()
        });
        results.createdBalances++;
      } else {
        // Update existing balance account
        console.log(`💰 Current balance - XP: ${balanceAccount.xp}, Atom: ${balanceAccount.atom}`);
        balanceAccount.atom += atomsToGive;
        results.updatedBalances++;
      }

      // Save the balance account
      await balanceAccount.save();
      console.log(`✅ Updated balance - XP: ${balanceAccount.xp}, Atom: ${balanceAccount.atom}`);

      results.success.push({
        email: student.email,
        name: student.name,
        userId: user._id.toString(),
        oldAtom: balanceAccount.atom - atomsToGive,
        newAtom: balanceAccount.atom,
        atomsAdded: atomsToGive
      });

    } catch (error) {
      console.error(`❌ Error processing ${student.email}:`, error);
      results.errors.push({
        email: student.email,
        name: student.name,
        error: error.message
      });
    }
  }

  return results;
}

// Generate summary report
function generateReport(results, atomsGiven) {
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
  const filename = `atoms_distribution_report_${timestamp}.json`;
  
  const report = {
    metadata: {
      generated: new Date().toISOString(),
      atomsGiven: atomsGiven,
      totalStudents: results.totalStudents
    },
    summary: {
      totalStudents: results.totalStudents,
      foundUsers: results.foundUsers,
      updatedBalances: results.updatedBalances,
      createdBalances: results.createdBalances,
      successRate: ((results.foundUsers / results.totalStudents) * 100).toFixed(2) + '%',
      totalAtomsDistributed: results.success.reduce((sum, s) => sum + s.atomsAdded, 0)
    },
    success: results.success,
    errors: results.errors
  };

  return {
    content: JSON.stringify(report, null, 2),
    filename: filename
  };
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting Atoms Distribution Script');
    console.log('CONNECTION_STRING:', CONNECTION_STRING ? 'Available' : 'Missing');
    
    await conectarAoMongoDB(CONNECTION_STRING);
    
    // Read students from JSON
    const students = readStudentsFromJSON();
    
    // Give atoms to users
    const atomsToGive = 2500;
    const results = await giveAtomsToUsers(students, atomsToGive);
    
    // Generate and save report
    const report = generateReport(results, atomsToGive);
    const reportPath = path.resolve(report.filename);
    fs.writeFileSync(reportPath, report.content);
    
    // Console output
    console.log('\n' + '='.repeat(60));
    console.log('🎉 ATOMS DISTRIBUTION COMPLETED');
    console.log('='.repeat(60));
    
    console.log(`\n📊 SUMMARY:`);
    console.log(`   Total Students: ${results.totalStudents}`);
    console.log(`   Users Found: ${results.foundUsers}`);
    console.log(`   Balance Accounts Updated: ${results.updatedBalances}`);
    console.log(`   Balance Accounts Created: ${results.createdBalances}`);
    console.log(`   Success Rate: ${((results.foundUsers / results.totalStudents) * 100).toFixed(2)}%`);
    console.log(`   Total Atoms Distributed: ${results.success.reduce((sum, s) => sum + s.atomsAdded, 0).toLocaleString()}`);
    
    if (results.errors.length > 0) {
      console.log(`\n❌ ERRORS (${results.errors.length}):`);
      results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.name} (${error.email}): ${error.error}`);
      });
    }
    
    console.log(`\n✅ SUCCESSFUL UPDATES (${results.success.length}):`);
    results.success.slice(0, 5).forEach((success, index) => {
      console.log(`   ${index + 1}. ${success.name}: ${success.oldAtom} → ${success.newAtom} atoms (+${success.atomsAdded})`);
    });
    
    if (results.success.length > 5) {
      console.log(`   ... and ${results.success.length - 5} more successful updates`);
    }
    
    console.log(`\n📁 Report saved to: ${reportPath}`);
    
    await mongoose.disconnect();
    console.log('\n✅ Script completed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Error in execution:', error);
    await mongoose.disconnect();
    process.exit(1);
  }
}

main();