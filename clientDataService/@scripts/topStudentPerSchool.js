import mongoose from 'mongoose';
import fs from 'fs';
import path from 'path';
import { UserModel } from '../src/domain/models/userModel.js';
import { SchoolModel } from '../src/domain/models/school/schoolModel.js';
import BalanceAccountModel from '../src/domain/models/balanceAccountModel.js';
import dotenv from 'dotenv';

dotenv.config();

const CONNECTION_STRING = process.env.CONNECTION_STRING;

// Conecta ao MongoDB
async function conectarAoMongoDB(mongodbUrl) {
  try {
    await mongoose.connect(mongodbUrl, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Conectado ao MongoDB');
  } catch (error) {
    console.error('Falha ao conectar ao MongoDB:', error);
    process.exit(1);
  }
}

// Filtra usuários teste (Padrão ALUNO X)
function filtrarUsuariosTeste(usuarios) {
  const filtrados = usuarios.filter(usuario => {
    // Pula usuários sem nome
    if (!usuario.name) return true;
    
    // Verifica se o nome corresponde ao padrão "ALUNO X" onde X é um número
    const padrao = /^aluno\s+\d+$/i;
    return !padrao.test(usuario.name.trim());
  });
  
  console.log(`Filtrados ${usuarios.length - filtrados.length} usuários de teste com nomes no padrão "ALUNO X"`);
  return filtrados;
}

// Busca o top student de cada escola
async function buscarTopStudentPorEscola() {
  try {
    console.log('Buscando todas as escolas...');
    
    // Busca todas as escolas
    const escolas = await SchoolModel.find({ inactive: false }).sort({ name: 1 });
    console.log(`Encontradas ${escolas.length} escolas ativas`);
    
    const resultados = [];
    
    for (const escola of escolas) {
      console.log(`\nProcessando escola: ${escola.name}`);
      
      // Busca todos os estudantes desta escola
      const estudantes = await UserModel.find({
        schoolId: escola._id,
        role: 'student'
      }).populate('balance');
      
      console.log(`Encontrados ${estudantes.length} estudantes na escola ${escola.name}`);
      
      // Filtra usuários de teste
      const estudantesReais = filtrarUsuariosTeste(estudantes);
      
      if (estudantesReais.length === 0) {
        console.log(`Nenhum estudante real encontrado na escola ${escola.name}`);
        resultados.push({
          escola: escola.name,
          escolaId: escola._id.toString(),
          topStudent: null,
          totalEstudantes: 0,
          mediaXP: 0
        });
        continue;
      }
      
      // Busca os balances de todos os estudantes
      const studentIds = estudantesReais.map(s => s._id);
      const balances = await BalanceAccountModel.find({
        ownerId: { $in: studentIds }
      });
      
      // Mapeia estudantes com seus balances
      const estudantesComBalance = estudantesReais.map(estudante => {
        const balance = balances.find(b => b.ownerId.toString() === estudante._id.toString());
        return {
          ...estudante.toObject(),
          xp: balance ? balance.xp : 0,
          atom: balance ? balance.atom : 0
        };
      });
      
      // Ordena por XP (decrescente) e pega o primeiro
      const estudantesOrdenados = estudantesComBalance.sort((a, b) => b.xp - a.xp);
      const topStudent = estudantesOrdenados[0];
      
      // Calcula média de XP da escola
      const mediaXP = estudantesComBalance.reduce((sum, s) => sum + s.xp, 0) / estudantesComBalance.length;
      
      console.log(`Top student da escola ${escola.name}: ${topStudent.name} (XP: ${topStudent.xp})`);
      
      resultados.push({
        escola: escola.name,
        escolaId: escola._id.toString(),
        topStudent: {
          nome: topStudent.name,
          email: topStudent.email,
          username: topStudent.username,
          schoolYear: topStudent.schoolYear,
          enrollmentId: topStudent.enrollmentId,
          xp: topStudent.xp,
          atom: topStudent.atom,
          userId: topStudent._id.toString()
        },
        totalEstudantes: estudantesComBalance.length,
        mediaXP: Math.round(mediaXP * 100) / 100,
        rankingCompleto: estudantesOrdenados.slice(0, 5).map((s, index) => ({
          posicao: index + 1,
          nome: s.name,
          email: s.email,
          xp: s.xp,
          atom: s.atom
        }))
      });
    }
    
    return resultados;
    
  } catch (error) {
    console.error('Erro ao buscar top students por escola:', error);
    throw error;
  }
}

// Gera relatório em CSV
function gerarRelatorioCSV(resultados) {
  // Cabeçalho principal
  const cabecalhoPrincipal = [
    'Escola',
    'Total de Estudantes',
    'Média XP da Escola',
    'Top Student - Nome',
    'Top Student - Email',
    'Top Student - Username',
    'Top Student - Ano Escolar',
    'Top Student - Matrícula',
    'Top Student - XP',
    'Top Student - Atom'
  ].join(',');
  
  // Linhas principais
  const linhasPrincipais = resultados.map(r => [
    `"${r.escola}"`,
    r.totalEstudantes,
    r.mediaXP,
    `"${r.topStudent ? r.topStudent.nome : 'N/A'}"`,
    `"${r.topStudent ? r.topStudent.email : 'N/A'}"`,
    `"${r.topStudent ? r.topStudent.username : 'N/A'}"`,
    r.topStudent ? r.topStudent.schoolYear || 'N/A' : 'N/A',
    `"${r.topStudent ? r.topStudent.enrollmentId || 'N/A' : 'N/A'}"`,
    r.topStudent ? r.topStudent.xp : 0,
    r.topStudent ? r.topStudent.atom : 0
  ].join(','));
  
  // Ranking detalhado por escola
  const rankingDetalhado = [];
  
  resultados.forEach(resultado => {
    if (resultado.rankingCompleto && resultado.rankingCompleto.length > 0) {
      rankingDetalhado.push('');
      rankingDetalhado.push(`"Ranking da Escola: ${resultado.escola}"`);
      rankingDetalhado.push('Posição,Nome,Email,XP,Atom');
      
      resultado.rankingCompleto.forEach(estudante => {
        rankingDetalhado.push([
          estudante.posicao,
          `"${estudante.nome}"`,
          `"${estudante.email}"`,
          estudante.xp,
          estudante.atom
        ].join(','));
      });
    }
  });
  
  // Estatísticas gerais
  const estatisticasGerais = [
    '',
    'ESTATÍSTICAS GERAIS',
    `Total de Escolas,${resultados.length}`,
    `Escolas com Estudantes,${resultados.filter(r => r.topStudent).length}`,
    `Escolas sem Estudantes,${resultados.filter(r => !r.topStudent).length}`,
    `Média XP Geral,${Math.round(resultados.reduce((sum, r) => sum + r.mediaXP, 0) / resultados.length * 100) / 100}`,
    `Maior XP Individual,${Math.max(...resultados.map(r => r.topStudent ? r.topStudent.xp : 0))}`,
    `Menor XP Individual,${Math.min(...resultados.filter(r => r.topStudent).map(r => r.topStudent.xp))}`,
    '',
    'TOP 10 ESCOLAS POR MÉDIA XP',
    'Posição,Escola,Média XP,Total Estudantes'
  ];
  
  // Top 10 escolas por média XP
  const topEscolasPorMedia = resultados
    .filter(r => r.topStudent) // Apenas escolas com estudantes
    .sort((a, b) => b.mediaXP - a.mediaXP)
    .slice(0, 10)
    .map((escola, index) => [
      index + 1,
      `"${escola.escola}"`,
      escola.mediaXP,
      escola.totalEstudantes
    ].join(','));
  
  estatisticasGerais.push(...topEscolasPorMedia);
  
  // Monta o CSV completo
  const csvDados = [
    cabecalhoPrincipal,
    ...linhasPrincipais,
    ...rankingDetalhado,
    ...estatisticasGerais
  ].join('\n');
  
  return csvDados;
}

// Execução principal
async function main() {
  try {
    await conectarAoMongoDB(CONNECTION_STRING);
    
    console.log('Iniciando busca do top student por escola...');
    const resultados = await buscarTopStudentPorEscola();
    
    // Gera relatório CSV
    const csvDados = gerarRelatorioCSV(resultados);
    
    // Salva arquivo CSV
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
    const nomeArquivo = `./top_student_per_school_${timestamp}.csv`;
    fs.writeFileSync(path.resolve(nomeArquivo), csvDados);
    
    console.log(`\n===== RELATÓRIO TOP STUDENT POR ESCOLA =====`);
    console.log(`Total de escolas processadas: ${resultados.length}`);
    console.log(`Escolas com estudantes: ${resultados.filter(r => r.topStudent).length}`);
    console.log(`Escolas sem estudantes: ${resultados.filter(r => !r.topStudent).length}`);
    
    console.log('\n===== TOP 5 ESCOLAS POR MÉDIA XP =====');
    const top5Escolas = resultados
      .filter(r => r.topStudent)
      .sort((a, b) => b.mediaXP - a.mediaXP)
      .slice(0, 5);
    
    top5Escolas.forEach((escola, index) => {
      console.log(`${index + 1}. ${escola.escola}: ${escola.mediaXP} XP (${escola.totalEstudantes} estudantes)`);
      console.log(`   Top student: ${escola.topStudent.nome} (${escola.topStudent.xp} XP)`);
    });
    
    console.log('\n===== TOP 5 ESTUDANTES GERAIS =====');
    const todosTopStudents = resultados
      .filter(r => r.topStudent)
      .map(r => r.topStudent)
      .sort((a, b) => b.xp - a.xp)
      .slice(0, 5);
    
    todosTopStudents.forEach((student, index) => {
      const escola = resultados.find(r => r.topStudent && r.topStudent.userId === student.userId);
      console.log(`${index + 1}. ${student.nome} (${escola.escola}): ${student.xp} XP`);
    });
    
    console.log(`\nRelatório completo salvo em: ${nomeArquivo}`);
    
    await mongoose.disconnect();
    return true;
    
  } catch (error) {
    console.error('Erro na execução:', error);
    await mongoose.disconnect();
    process.exit(1);
  }
}

main();
