import ApiCaller from "./ApiCaller.js";
import { env } from "../../env/index.js";

if (!env.SAPS_API_URL) {
  console.warn("Missing env: SAPS_API_URL - SAPS integration will not work");
}

const api = new ApiCaller(env.SAPS_API_URL || "");

export default class SapsApi {
  /**
   * Gets comment information from SAPS
   * @param {Object} params - Parameters
   * @param {string} params.commentId - Comment ID
   * @param {string} params.token - Authentication token
   * @returns {Promise<Object>} Comment information including video details
   */
  static async getCommentInfo({ commentId, token }) {
    try {
      if (!env.SAPS_API_URL) {
        throw new Error('SAPS_API_URL not configured');
      }
      
      if (!token) {
        throw new Error('Missing Authorization token');
      }
      
      const parsedToken = token.includes(' ') ? token.split(' ')[1] : token;
      const url = `/video/comment/${commentId}/info`;
      
      console.log(`Fetching comment info from SAPS: ${url}`);
      
      const response = await api.get(
        url,
        {},
        parsedToken
      );

      // ApiCaller.get() already returns response.data, so we don't need to access .data again
      return response;
    } catch (error) {
      console.error('Error fetching comment info from SAPS:', error);
      throw error;
    }
  }
}