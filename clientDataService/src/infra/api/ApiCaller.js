import axios from 'axios';

class ApiCaller {
    constructor(baseURL) {
        this.baseURL = baseURL;
    }

    createHeaders(jwtToken) {
        const headers = {
            'Content-Type': 'application/json',
        };
        
        if (jwtToken) {
            headers['Authorization'] = `Bearer ${jwtToken}`;
        }
        
        return headers;
    }

    async get(url, params = {}, jwtToken = null) {
        try {
            const config = {
                headers: this.createHeaders(jwtToken),
                params
            };

            const response = await axios.get(`${this.baseURL}${url}`, config);
            return response.data;
        } catch (error) {
            console.error(`Error in GET ${url}:`, error.response?.data || error.message);
            throw error;
        }
    }

    async post(url, data = {}, params = {}, jwtToken = null) {
        try {
            const config = {
                headers: this.createHeaders(jwtToken),
                params
            };

            const response = await axios.post(`${this.baseURL}${url}`, data, config);
            return response.data;
        } catch (error) {
            console.error(`Error in POST ${url}:`, error.response?.data || error.message);
            throw error;
        }
    }

    async patch(url, data = {}, params = {}, jwtToken = null) {
        try {
            const config = {
                headers: this.createHeaders(jwtToken),
                params
            };

            const response = await axios.patch(`${this.baseURL}${url}`, data, config);
            return response.data;
        } catch (error) {
            console.error(`Error in PATCH ${url}:`, error.response?.data || error.message);
            throw error;
        }
    }
}

export default ApiCaller;