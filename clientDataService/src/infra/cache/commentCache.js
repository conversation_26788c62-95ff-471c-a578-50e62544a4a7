/**
 * Comment Cache Service
 * 
 * Provides in-memory caching for comment data to reduce SAPS API calls
 * and improve teacher panel performance.
 */

class CommentCache {
    constructor() {
        this.cache = new Map();
        this.defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL
        this.maxSize = 1000; // Maximum cache entries
        
        // Cleanup expired entries every 2 minutes
        setInterval(() => this.cleanup(), 2 * 60 * 1000);
    }

    /**
     * Generate cache key for comment data
     */
    generateKey(commentId, type = 'comment') {
        return `${type}:${commentId}`;
    }

    /**
     * Set cache entry with TTL
     */
    set(key, value, ttl = this.defaultTTL) {
        // Implement LRU eviction if cache is full
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        const entry = {
            value,
            timestamp: Date.now(),
            ttl
        };

        this.cache.set(key, entry);
        console.log(`Cache SET: ${key} (TTL: ${ttl}ms)`);
    }

    /**
     * Get cache entry if not expired
     */
    get(key) {
        const entry = this.cache.get(key);
        
        if (!entry) {
            return null;
        }

        const now = Date.now();
        if (now - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            console.log(`Cache EXPIRED: ${key}`);
            return null;
        }

        console.log(`Cache HIT: ${key}`);
        return entry.value;
    }

    /**
     * Check if key exists and is not expired
     */
    has(key) {
        return this.get(key) !== null;
    }

    /**
     * Delete cache entry
     */
    delete(key) {
        const deleted = this.cache.delete(key);
        if (deleted) {
            console.log(`Cache DELETE: ${key}`);
        }
        return deleted;
    }

    /**
     * Clear all cache entries
     */
    clear() {
        const size = this.cache.size;
        this.cache.clear();
        console.log(`Cache CLEAR: ${size} entries removed`);
    }

    /**
     * Get cache statistics
     */
    getStats() {
        return {
            size: this.cache.size,
            maxSize: this.maxSize,
            defaultTTL: this.defaultTTL
        };
    }

    /**
     * Cleanup expired entries
     */
    cleanup() {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
                this.cache.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            console.log(`Cache CLEANUP: ${cleanedCount} expired entries removed`);
        }
    }

    /**
     * Cache comment info with optimized TTL based on comment age
     */
    cacheCommentInfo(commentId, commentData) {
        // Newer comments get shorter TTL (more likely to change)
        // Older comments get longer TTL (less likely to change)
        const commentAge = Date.now() - new Date(commentData.createdAt).getTime();
        const dayInMs = 24 * 60 * 60 * 1000;
        
        let ttl;
        if (commentAge < dayInMs) {
            ttl = 2 * 60 * 1000; // 2 minutes for recent comments
        } else if (commentAge < 7 * dayInMs) {
            ttl = 10 * 60 * 1000; // 10 minutes for week-old comments
        } else {
            ttl = 30 * 60 * 1000; // 30 minutes for older comments
        }

        const key = this.generateKey(commentId, 'comment');
        this.set(key, commentData, ttl);
    }

    /**
     * Get cached comment info
     */
    getCachedCommentInfo(commentId) {
        const key = this.generateKey(commentId, 'comment');
        return this.get(key);
    }

    /**
     * Cache video info (longer TTL since videos change less frequently)
     */
    cacheVideoInfo(videoId, videoData) {
        const key = this.generateKey(videoId, 'video');
        this.set(key, videoData, 15 * 60 * 1000); // 15 minutes TTL
    }

    /**
     * Get cached video info
     */
    getCachedVideoInfo(videoId) {
        const key = this.generateKey(videoId, 'video');
        return this.get(key);
    }

    /**
     * Batch cache multiple comment infos
     */
    batchCacheComments(commentsData) {
        commentsData.forEach(comment => {
            if (comment.id) {
                this.cacheCommentInfo(comment.id, comment);
            }
        });
    }

    /**
     * Batch get multiple cached comments
     */
    batchGetCachedComments(commentIds) {
        const cached = {};
        const missing = [];

        commentIds.forEach(commentId => {
            const cachedData = this.getCachedCommentInfo(commentId);
            if (cachedData) {
                cached[commentId] = cachedData;
            } else {
                missing.push(commentId);
            }
        });

        return { cached, missing };
    }

    /**
     * Invalidate cache entries related to a specific video
     */
    invalidateVideoComments(videoId) {
        let invalidatedCount = 0;
        
        for (const [key, entry] of this.cache.entries()) {
            if (key.startsWith('comment:') && entry.value.videoId === videoId) {
                this.cache.delete(key);
                invalidatedCount++;
            }
        }

        console.log(`Cache INVALIDATE: ${invalidatedCount} entries for video ${videoId}`);
    }

    /**
     * Preload frequently accessed comments
     */
    async preloadFrequentComments(commentIds, sapsApi, token) {
        const { missing } = this.batchGetCachedComments(commentIds);
        
        if (missing.length === 0) {
            return;
        }

        console.log(`Cache PRELOAD: Loading ${missing.length} missing comments`);
        
        // Load missing comments in parallel
        const loadPromises = missing.map(async (commentId) => {
            try {
                const commentData = await sapsApi.getCommentInfo({ commentId, token });
                if (commentData) {
                    this.cacheCommentInfo(commentId, commentData);
                }
            } catch (error) {
                console.warn(`Failed to preload comment ${commentId}:`, error.message);
            }
        });

        await Promise.allSettled(loadPromises);
    }
}

// Singleton instance
const commentCache = new CommentCache();

export default commentCache;
