import { getCommentsByGroupsBatchService } from '../../../application/services/lessonMetrics/getCommentsByGroupsBatchService.js';
import { CustomError } from "../../../utils/customErrors/index.js";
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

/**
 * CDS Batch handler for getting comments from multiple groups
 * This optimizes database queries by batching group requests
 */
export async function handler(event) {
  try {
    await connectToDB();
    
    console.log("CDS getCommentsByGroupsBatch handler triggered");
    
    if (!event.body) {
      return apiResponse(400, { body: { message: 'Missing request body' } });
    }

    const body = JSON.parse(event.body);
    const { groupIds, videoId, isDoubt = true, page = 1, limit = 20 } = body;

    // Validate input
    if (!groupIds || !Array.isArray(groupIds) || groupIds.length === 0) {
      return apiResponse(400, { body: { message: 'groupIds array is required and cannot be empty' } });
    }

    if (groupIds.length > 100) {
      return apiResponse(400, { body: { message: 'Maximum 100 groups allowed per batch request' } });
    }

    // Remove duplicates and filter out invalid group IDs
    const uniqueGroupIds = [...new Set(groupIds.filter(id => id && typeof id === 'string'))];

    console.log(`CDS processing batch request for ${uniqueGroupIds.length} groups:`, {
      originalCount: groupIds.length,
      uniqueCount: uniqueGroupIds.length,
      videoId,
      isDoubt,
      page,
      limit
    });

    const result = await getCommentsByGroupsBatchService({
      groupIds: uniqueGroupIds,
      videoId,
      isDoubt,
      page,
      limit
    });

    console.log('CDS getCommentsByGroupsBatch success:', {
      totalComments: result.comments?.length || 0,
      groupResults: result.groupResults?.length || 0,
      successfulGroups: result.groupResults?.filter(g => g.success).length || 0
    });

    return apiResponse(200, { body: result });
  } catch (error) {
    console.error("CDS getCommentsByGroupsBatch error:", {
      message: error.message,
      stack: error.stack,
      body: event.body
    });

    if (error instanceof CustomError) {
      return apiResponse(error.code, { body: { message: error.message } });
    }

    return apiResponse(500, { body: { message: error.message } });
  }
}
