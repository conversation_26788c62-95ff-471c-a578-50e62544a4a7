import connectToDB from '../../../infra/libs/mongodb/connect.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import getLoginStreakXP from '../../../application/services/user/getLoginStreakXP.js';

export async function handler(event) {
  try {
    console.log("event", event);
    await connectToDB();
    const { id: userId} = event.requestContext.authorizer || {};
    const response = await getLoginStreakXP(userId)
    return apiResponse(200, { streak: response});
  } catch (error) {
    console.error("Error getting user statistics:", error);
    return apiResponse(404, { body: { message: error.message } });
  }
} 