import SchoolRepository from '../../../domain/repositories/schoolRepository.js';
import SchoolAggregateRoot from '../../../domain/aggregates/school/schoolAggregateRoot.js';
import { NotFoundError, ValidationError } from "../../../utils/customErrors/index.js";

export default async function updateSchoolService({ schoolId, name = null, address = null, coordinates = null, inactive = null }) {
    const existingSchool = await SchoolRepository.findById(schoolId)

    if (!existingSchool)
        throw new NotFoundError("School not found")

    const schoolAggregate = SchoolAggregateRoot.fromData({ ...existingSchool })

    if (!schoolAggregate)
        throw new ValidationError("School format is invalid")

    // get the update body 
    const dataToUpdate = SchoolAggregateRoot.findUpdateBody({ name, address, coordinates, inactive });

    const updatedSchool = await SchoolRepository.update(schoolAggregate._id, dataToUpdate);

    return updatedSchool;
}