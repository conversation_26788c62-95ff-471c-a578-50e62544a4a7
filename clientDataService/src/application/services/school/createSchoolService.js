import SchoolRepository from "../../../domain/repositories/schoolRepository.js";
import ContractRepository from "../../../domain/repositories/contractRepository.js";
import SchoolAggregateRoot from "../../../domain/aggregates/school/schoolAggregateRoot.js";
import {
  NotFoundError,
  ValidationError,
} from "../../../utils/customErrors/index.js";
import { GroupModel } from "../../../modules/groups/schema/groupSchema.js";

export default async function createSchoolService({
  contractId,
  name,
  address,
  coordinates,
  groupId,
}) {
  const contract = await ContractRepository.findById(contractId);

  if (!contract) throw new NotFoundError("Contract not found");

  const schoolAggregate = SchoolAggregateRoot.fromData({
    contractId,
    name,
    address,
    coordinates,
    inactive: false,
    groupId,
  });

  // group contractId must be the same as the group contractId
  const group = await GroupModel.findById(groupId);

  if (!group || group.contractId.toString() !== contractId)
    throw new ValidationError(
      "Group contractId must be the same as the contract contractId"
    );

  if (!schoolAggregate)
    throw new ValidationError("New created school is invalid");

  const newSchool = await SchoolRepository.create(schoolAggregate);

  return newSchool;
}
