import SchoolRepository from '../../../domain/repositories/schoolRepository.js';
import SchoolAggregateRoot from '../../../domain/aggregates/school/schoolAggregateRoot.js';
import { NotFoundError, ValidationError } from "../../../utils/customErrors/index.js";

export default async function addStudentsToSchoolService({ schoolId, studentCpfs }) {
    const existingSchool = await SchoolRepository.findById(schoolId);

    if (!existingSchool) {
        throw new NotFoundError("School not found");
    }

    const schoolAggregate = SchoolAggregateRoot.fromData({ ...existingSchool });

    if (!schoolAggregate) {
        throw new ValidationError("School format is invalid");
    }

    // Check if the school is inactive
    if (schoolAggregate.inactive) {
        throw new ValidationError("Cannot update an inactive school");
    }

    // Merge existing CPFs with new CPFs, avoiding duplicates
    const updatedStudentCpfs = Array.from(new Set([
        ...(existingSchool.studentsCPF || []),
        ...studentCpfs
    ]));

    // Get the update body
    const dataToUpdate = {
        studentsCPF: updatedStudentCpfs,
        ...SchoolAggregateRoot.findUpdateBody({
            name: schoolAggregate.name,
            address: schoolAggregate.address,
            coordinates: schoolAggregate.coordinates,
            inactive: schoolAggregate.inactive
        })
    };

    // Update the school
    const updatedSchool = await SchoolRepository.update(schoolAggregate._id, dataToUpdate);

    return updatedSchool;
}