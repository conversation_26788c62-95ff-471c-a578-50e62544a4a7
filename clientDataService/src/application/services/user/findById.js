import UserRepository from '../../../domain/repositories/userRepository.js';
import TrackingRepository from '../../../domain/repositories/trackingRepository.js';

async function findUserByIdService(userId) {
    const userRepository = new UserRepository();
    const user = await userRepository.findById(userId);

    if (!user) {
        throw new Error(`User with ID ${userId} not found`);
    }

    // Check for recent login activity and add new login if needed
    await checkAndAddLoginActivity(userId);

    return user;
}

/**
 * Checks if the user has logged in within the last 24 hours.
 * If not, creates a new login record to track this data fetch as a login activity.
 * @param {string} userId - The user ID to check login activity for
 */
async function checkAndAddLoginActivity(userId) {
    try {
        // Check for existing login in the last 24 hours using repository
        const hasRecentLogin = await TrackingRepository.hasRecentLogin(userId, 24);

        if (!hasRecentLogin) {
            // No recent login found, create a new login record using repository
            await TrackingRepository.addLoginTimeStamp(userId, {
                name: 'User Data Access',
                source: 'data_fetch',
                description: 'Automatic login created during user data fetch'
            });

            console.log(`[findUserByIdService] Created new login record for user ${userId} - no login in last 24 hours`);
        } else {
            console.log(`[findUserByIdService] User ${userId} has recent login activity, no new record needed`);
        }
    } catch (error) {
        // Log the error but don't fail the main operation
        console.error(`[findUserByIdService] Error checking/adding login activity for user ${userId}:`, error);
    }
}

export default findUserByIdService;