import MatrixisHandler from '../../../infra/Atomize/Matrixis/messageHandler.js';
import UserRepository from '../../../domain/repositories/userRepository.js'; 

const userRepository = new UserRepository();

async function getLoginStreakXP(userId) {    
    try {
        const statistics = await userRepository.getUserGeneralStatistics(userId);
        const streak = statistics?.loginStreak?.currentStreak;
        if(!streak){
            throw new Error("Failed to get loginStreak")
        }
        await MatrixisHandler.publishLoginStreakXP({userId, streak})
        return streak;
    } catch (error) {
        throw new Error(`${error.message}`);
    }
}

export default getLoginStreakXP;