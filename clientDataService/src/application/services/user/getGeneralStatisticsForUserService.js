import UserRepository from '../../../domain/repositories/userRepository.js'; 

const userRepository = new UserRepository();

async function getGeneralStatisticsForUserService(userId) {    
    try {
        const statistics = await userRepository.getUserGeneralStatistics(userId);
        return statistics;
    } catch (error) {
        throw new Error(`Erro ao obter estatísticas do usuário: ${error.message}`);
    }
}

export default getGeneralStatisticsForUserService;