import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import UserRepository from '../../../domain/repositories/userRepository.js';
import TrackingRepository from '../../../domain/repositories/trackingRepository.js';

async function loginService({ email, password }) {
    const userRepository = new UserRepository();
    const user = await userRepository.findByEmail(email);

    if (!user) {
        throw new Error('Invalid email or password');
    }
    const passwordMatch = await bcrypt.compare(password, user.password);

    if (!passwordMatch) {
        throw new Error('Invalid email or password');
    }

    TrackingRepository.addLoginTimeStamp(user._id)

    // Delete password key before sending the data
    const userWithoutPassword = user.toObject();
    delete userWithoutPassword.password;

  // Generate JWT token
  const token = jwt.sign(
    { id: user._id, role: user.role },
    process.env.JWT_SECRET,
    { expiresIn: "48h" }
  );

    const data = { token, user: userWithoutPassword };
    return data;
};

export default loginService;