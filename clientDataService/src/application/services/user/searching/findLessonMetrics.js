import UserRepository from "../../../../domain/repositories/userRepository.js";
const userRepository = new UserRepository();

export async function findLessonMetricsService({ userId, playlistId = null, olympiadId = null }) {
    if (typeof userId !== 'string') {
        throw new Error('Invalid userId: userId must be a string.');
    }

    const questionMetrics = await userRepository.findLessonMetrics({ userId, olympiadId, playlistId });

    return questionMetrics;
}
