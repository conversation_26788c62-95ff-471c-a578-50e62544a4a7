import UserRepository from "../../../domain/repositories/userRepository.js";
import { NotFoundError } from "../../../utils/customErrors/index.js";

const userRepository = new UserRepository();

export default async function getSingleLessonWithMetricsService({ id, videoId, playlistId }) {
    // find user
    const userAggregate = await userRepository.findByIdWithMetrics({ userId: id, metricsArray: ['lessonMetrics'] });


    // mount video metric
    const lessonMetric = userAggregate.getSingleLessonMetric({ videoId, playlistId, userId: id });


    if (!lessonMetric)
        throw new NotFoundError("lessonMetric");


    return lessonMetric;
}