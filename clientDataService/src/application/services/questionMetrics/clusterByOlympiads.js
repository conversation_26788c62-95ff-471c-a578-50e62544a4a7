import UserRepository from "../../../domain/repositories/userRepository.js";
import { ValidationError } from "../../../utils/customErrors/index.js";
const userRepository = new UserRepository();

export async function clusterByOlympiadsService({ userId }) {
    if (typeof userId !== 'string') {
        throw new ValidationError('userId must be a string.');
    }

    const allQuestionMetrics = await userRepository.findQuestionMetrics({ userId });

    let totalNumberOfQuestions = allQuestionMetrics.length;
    let totalCorrectAnswers = 0;

    const metricsMap = new Map();

    allQuestionMetrics.forEach((question) => {
        const { olympiadId, isCorrect } = question;

        if (!olympiadId) {
            return;
        }

        if (!metricsMap.has(olympiadId)) {
            metricsMap.set(olympiadId, {
                olympiadId: olympiadId,
                numberOfCorrectAnswers: 0,
                numberOfWrongAnswers: 0,
            });
        }

        const metrics = metricsMap.get(olympiadId);
        if (isCorrect) {
            metrics.numberOfCorrectAnswers++;
            totalCorrectAnswers++;
        } else {
            metrics.numberOfWrongAnswers++;
        }
    });

    const metricsResume = { total: totalNumberOfQuestions, numberOfCorrectAnswers: totalCorrectAnswers };
    const metrics = Array.from(metricsMap.values());
    return { metricsResume, metrics };
}