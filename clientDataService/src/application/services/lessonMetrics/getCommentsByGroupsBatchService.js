import LessonMetrics from '../../../domain/models/metrics/lessonMetricsModel.js';

/**
 * Batch service to get comments from multiple groups efficiently
 * This optimizes database queries by using aggregation pipelines
 */
export async function getCommentsByGroupsBatchService({
  groupIds,
  videoId,
  isDoubt = true,
  page = 1,
  limit = 20
}) {
  console.log('CDS getCommentsByGroupsBatchService called with:', {
    groupCount: groupIds.length,
    videoId,
    isDoubt,
    page,
    limit
  });

  try {
    const allComments = [];
    const groupResults = [];

    // Build aggregation pipeline for efficient batch querying
    const matchStage = {
      groupId: { $in: groupIds }
    };

    if (videoId) {
      matchStage.videoId = parseInt(videoId);
    }

    // Only include documents that have doubts if isDoubt is true
    if (isDoubt) {
      matchStage.doubts = { $exists: true, $ne: [] };
    }

    console.log('CDS MongoDB match stage:', matchStage);

    const aggregationPipeline = [
      { $match: matchStage },
      {
        $unwind: {
          path: '$doubts',
          preserveNullAndEmptyArrays: false
        }
      },
      {
        $addFields: {
          'doubts.groupId': '$groupId',
          'doubts.userId': '$userId',
          'doubts.videoId': '$videoId'
        }
      },
      {
        $replaceRoot: {
          newRoot: '$doubts'
        }
      },
      {
        $sort: {
          createdAt: -1 // Sort by creation date, newest first
        }
      }
    ];

    console.log('CDS executing aggregation pipeline...');
    const startTime = Date.now();
    
    const results = await LessonMetrics.aggregate(aggregationPipeline);
    
    const queryTime = Date.now() - startTime;
    console.log(`CDS aggregation completed in ${queryTime}ms, found ${results.length} total doubts`);

    // Group results by groupId for reporting
    const commentsByGroup = new Map();
    
    results.forEach(comment => {
      const groupId = comment.groupId;
      if (!commentsByGroup.has(groupId)) {
        commentsByGroup.set(groupId, []);
      }
      commentsByGroup.get(groupId).push(comment);
    });

    // Create group results summary
    groupIds.forEach(groupId => {
      const groupComments = commentsByGroup.get(groupId) || [];
      groupResults.push({
        groupId,
        success: true,
        commentCount: groupComments.length
      });
    });

    // Apply pagination to the aggregated results
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedComments = results.slice(startIndex, endIndex);

    // Transform the results to match expected format
    const transformedComments = paginatedComments.map(comment => ({
      id: comment.commentId,
      commentId: comment.commentId,
      userId: comment.userId,
      videoId: comment.videoId,
      groupId: comment.groupId,
      doubtStatus: comment.status,
      createdAt: comment.createdAt || new Date().toISOString(),
      updatedAt: comment.updatedAt || new Date().toISOString()
    }));

    const successfulGroups = groupResults.filter(g => g.success).length;
    const totalComments = results.length;

    console.log('CDS getCommentsByGroupsBatchService completed:', {
      totalGroups: groupIds.length,
      successfulGroups,
      totalComments,
      paginatedComments: transformedComments.length,
      queryTime: `${queryTime}ms`
    });

    return {
      comments: transformedComments,
      groupResults,
      pagination: {
        page,
        limit,
        total: totalComments,
        totalPages: Math.ceil(totalComments / limit)
      },
      summary: {
        totalGroups: groupIds.length,
        successfulGroups,
        failedGroups: groupIds.length - successfulGroups,
        totalComments,
        queryTime
      }
    };

  } catch (error) {
    console.error('CDS getCommentsByGroupsBatchService error:', error);
    
    // Return error results for all groups
    const groupResults = groupIds.map(groupId => ({
      groupId,
      success: false,
      error: error.message,
      commentCount: 0
    }));

    return {
      comments: [],
      groupResults,
      pagination: {
        page,
        limit,
        total: 0,
        totalPages: 0
      },
      summary: {
        totalGroups: groupIds.length,
        successfulGroups: 0,
        failedGroups: groupIds.length,
        totalComments: 0
      }
    };
  }
}

/**
 * Alternative implementation using individual queries (fallback)
 * This can be used if the aggregation approach has issues
 */
export async function getCommentsByGroupsBatchServiceFallback({
  groupIds,
  videoId,
  isDoubt = true,
  page = 1,
  limit = 20
}) {
  console.log('CDS using fallback batch service');
  
  const allComments = [];
  const groupResults = [];
  const BATCH_SIZE = 20; // Process groups in batches

  try {
    // Process groups in batches to avoid overwhelming the database
    const groupBatches = [];
    for (let i = 0; i < groupIds.length; i += BATCH_SIZE) {
      groupBatches.push(groupIds.slice(i, i + BATCH_SIZE));
    }

    for (const batch of groupBatches) {
      const batchPromises = batch.map(async (groupId) => {
        try {
          const query = { groupId };
          if (videoId) {
            query.videoId = parseInt(videoId);
          }

          const lessonMetrics = await LessonMetrics.find(query);
          
          const groupComments = [];
          lessonMetrics.forEach(metric => {
            if (metric.doubts && metric.doubts.length > 0) {
              metric.doubts.forEach(doubt => {
                groupComments.push({
                  id: doubt.commentId,
                  commentId: doubt.commentId,
                  userId: metric.userId,
                  videoId: metric.videoId,
                  groupId: metric.groupId,
                  doubtStatus: doubt.status,
                  createdAt: doubt.createdAt || new Date().toISOString(),
                  updatedAt: doubt.updatedAt || new Date().toISOString()
                });
              });
            }
          });

          return {
            groupId,
            success: true,
            comments: groupComments,
            commentCount: groupComments.length
          };
        } catch (error) {
          console.error(`CDS error for group ${groupId}:`, error.message);
          return {
            groupId,
            success: false,
            error: error.message,
            comments: [],
            commentCount: 0
          };
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          const groupResult = result.value;
          groupResults.push({
            groupId: groupResult.groupId,
            success: groupResult.success,
            error: groupResult.error,
            commentCount: groupResult.commentCount
          });
          
          if (groupResult.success) {
            allComments.push(...groupResult.comments);
          }
        }
      });
    }

    // Sort and paginate
    const sortedComments = allComments.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedComments = sortedComments.slice(startIndex, endIndex);

    return {
      comments: paginatedComments,
      groupResults,
      pagination: {
        page,
        limit,
        total: sortedComments.length,
        totalPages: Math.ceil(sortedComments.length / limit)
      }
    };

  } catch (error) {
    console.error('CDS fallback batch service error:', error);
    throw error;
  }
}
