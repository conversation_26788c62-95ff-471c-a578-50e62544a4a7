import UserRepository from "../../../domain/repositories/userRepository.js";
import { NotFoundError } from "../../../utils/customErrors/index.js";
import { LessonMetricModel } from "../../../domain/models/metrics/lessonMetricsModel.js";

const userRepository = new UserRepository();

/**
 * Service to add or remove video comment metrics for a user
 * @param {Object} params - Service parameters
 * @param {string} params.userId - User identifier
 * @param {string} params.videoId - Video identifier
 * @param {string} params.commentId - Comment identifier
 * @param {boolean} params.isDoubt - Whether the comment is a doubt
 * @param {boolean} params.isRemove - Whether to remove the comment
 * @param {string} params.playlistId - Playlist identifier (optional)
 * @returns {Promise<Object>} Updated metrics or success message
 */
export default async function addOrRemoveVideoCommentMetricsService({ 
    userId, 
    videoId, 
    commentId, 
    isDoubt = false, 
    isRemove = false,
    playlistId = null
}) {
    console.log("DEBUG - addOrRemoveVideoCommentMetricsService called with:", { userId, videoId, commentId, isDoubt, isRemove, playlistId });
    
    try {
        // Try to find existing lessonMetric
        let lessonMetric = await LessonMetricModel.findOne({ 
            userId, 
            videoId, 
            playlistId 
        });

        if (lessonMetric) {
            console.log("DEBUG - Found existing lessonMetric, updating it");
            return await updateExistingLessonMetric(lessonMetric, { commentId, isDoubt, isRemove });
        } else {
            console.log("DEBUG - No existing lessonMetric found, creating new one");
            return await createNewLessonMetric({ 
                userId, 
                videoId, 
                commentId, 
                isDoubt, 
                playlistId
            });
        }
    } catch (error) {
        console.error("ERROR in addOrRemoveVideoCommentMetricsService:", error);
        throw error;
    }
}

/**
 * Create a new lesson metric
 * @param {Object} params - Parameters for new lesson metric
 * @returns {Promise<Object>} Result of creation
 */
async function createNewLessonMetric({ userId, videoId, commentId, isDoubt, playlistId }) {
    if (!commentId) {
        return {
            message: "Cannot create lesson metric without commentId",
            comments: [],
            doubts: []
        };
    }

    const newLessonMetric = {
        userId,
        videoId,
        playlistId,
        subject: "",
        olympiadId: null,
        type: "MODULE",
        videoOwnerId: "atomize",
        watched: false,
        tags: [],
        watchedAt: null,
        isLiked: false,
        comments: [commentId],
        doubts: isDoubt ? [{ commentId, status: -1 }] : []
    };

    console.log("DEBUG - Creating new LessonMetric without subject (subject stays in SAPS)");
    const savedMetric = await LessonMetricModel.create(newLessonMetric);
    
    return { 
        message: "New lesson metric created successfully", 
        comments: savedMetric.comments, 
        doubts: savedMetric.doubts 
    };
}

/**
 * Update existing lesson metric with comment operations
 * @param {Object} lessonMetric - Existing lesson metric
 * @param {Object} params - Update parameters
 * @returns {Promise<Object>} Updated lesson metric
 */
async function updateExistingLessonMetric(lessonMetric, { commentId, isDoubt, isRemove }) {
    // Ensure arrays exist
    if (!Array.isArray(lessonMetric.comments)) {
        lessonMetric.comments = [];
    }
    
    if (!Array.isArray(lessonMetric.doubts)) {
        lessonMetric.doubts = [];
    }
    
    if (isRemove) {
        // Remove comment and doubt
        lessonMetric.comments = lessonMetric.comments.filter(id => id !== commentId);
        lessonMetric.doubts = lessonMetric.doubts.filter(doubt => doubt.commentId !== commentId);
    } else {
        // Add comment if not already present
        if (commentId && !lessonMetric.comments.includes(commentId)) {
            lessonMetric.comments.push(commentId);
        }

        // Add doubt if it's a doubt and not already present
        if (isDoubt && commentId && !lessonMetric.doubts.find(doubt => doubt.commentId === commentId)) {
            lessonMetric.doubts.push({ commentId, status: -1 });
        }
    }

    const updatedMetric = await lessonMetric.save();
    
    return { 
        message: isRemove ? "Comment removed successfully" : "Comment added successfully", 
        comments: updatedMetric.comments, 
        doubts: updatedMetric.doubts 
    };
}