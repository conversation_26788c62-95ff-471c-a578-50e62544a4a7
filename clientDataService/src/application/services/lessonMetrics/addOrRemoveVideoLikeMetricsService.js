import UserRepository from "../../../domain/repositories/userRepository.js";
import { NotFoundError } from "../../../utils/customErrors/index.js";

const userRepository = new UserRepository();

export default async function addOrRemoveVideoLikeMetricsService({ userId, videoId, playlistId }) {
    // find user
    const userAggregate = await userRepository.findByIdWithMetrics({ userId: userId, metricsArray: ['lessonMetrics'] });

    if (!userAggregate)
        throw new NotFoundError('user not found')

    // mount video metric
    let lessonMetric = userAggregate.getSingleLessonMetric({ videoId, userId });

    if (!lessonMetric) {
        // Create a new lessonMetric
        const newLessonMetric = {
            userId,
            videoId,
            playlistId,
            subject: "",
            olympiadId: "",
            type: "MODULE",
            videoOwnerId: "atomize",
            watched: false,
            tags: [],
            watchedAt: null,
            isLiked: true
        };
        // Save the new lessonMetric
        lessonMetric = await userRepository.addLessonMetricValue(newLessonMetric);
        return { message: "New lessonMetric created and liked", isLiked: true };
    }

    // change liked to not liked or not liked to liked.
    const updatedLessonMetric = lessonMetric

    updatedLessonMetric.isLiked = !updatedLessonMetric.isLiked;

    const changedFields = await userRepository.updateLessonMetric(updatedLessonMetric);


    return changedFields;
}