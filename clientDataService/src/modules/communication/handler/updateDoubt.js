import { connectToDB } from '../../../utils/database/index.js';
import { apiResponse } from '../../../utils/apiResponse/index.js';
import { updateDoubt } from '../useCases/updateDoubt.js';

export async function handler(event) {
  try {
    await connectToDB();

    const { userId, role } = event.requestContext.authorizer;
    
    if (!userId) {
      return apiResponse(401, { error: 'Unauthorized: User ID not found' });
    }

    const { commentId } = event.pathParameters;
    if (!commentId) {
      return apiResponse(400, { error: 'commentId is required' });
    }

    let body;
    try {
      body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } catch (error) {
      return apiResponse(400, { error: 'Invalid JSON in request body' });
    }

    const { status } = body;
    if (status === undefined || status === null) {
      return apiResponse(400, { error: 'status is required' });
    }

    // Update doubt status
    const result = await updateDoubt({
      userId,
      role,
      commentId,
      status
    });

    return apiResponse(200, result);

  } catch (error) {
    console.error('Error in updateDoubt handler:', error);
    
    if (error.name === 'ValidationError') {
      return apiResponse(400, { error: error.message });
    }
    
    if (error.name === 'AuthorizationError') {
      return apiResponse(403, { error: error.message });
    }
    
    if (error.name === 'NotFoundError') {
      return apiResponse(404, { error: error.message });
    }
    
    return apiResponse(500, { error: 'Internal server error' });
  }
}
