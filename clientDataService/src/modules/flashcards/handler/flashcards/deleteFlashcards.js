import deleteFlashcardsUseCase from '../../useCases/flashcards/deleteFlashcards.js';
import { ValidationError } from '../../../../utils/customErrors/index.js';

/**
 * Handler for deleting multiple flashcards.
 * @param {Object} event - The event object containing request data.
 * @returns {Promise<Object>} - The response object.
 */
export async function handler(event) {
  try {
    // get userId from event.requestContext.authorizer.claims.sub
    const {id: userId} = event.requestContext.authorizer;
    if (!userId) {
      throw new ValidationError('User ID is required for this operation.');
    }

    // Parse request body
    let requestBody;
    try {
      requestBody = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } catch (error) {
      throw new ValidationError('Invalid JSON in request body.');
    }

    // Extract flashcard IDs from request body
    const { flashcardIds } = requestBody;

    if (!flashcardIds) {
      throw new ValidationError('flashcardIds is required in request body.');
    }

    // Call the use case
    const result = await deleteFlashcardsUseCase(flashcardIds, userId);

    return result;
  } catch (error) {
    console.error('Error in deleteFlashcards handler:', error);
    throw error;
  }
}
