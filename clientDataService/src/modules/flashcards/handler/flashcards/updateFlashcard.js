import updateFlashcard from "../../useCases/flashcards/updateFlashcard.js";

export async function handler(event) {
    try {
        const {updateData, flashcardId} = JSON.parse(event.body);
        
        if (!flashcardId) {
            throw new Error('Flashcard ID is required');
        }

        // Get userId from event.requestContext.authorizer.claims.sub
        const {id: userId} = event.requestContext.authorizer;
        
        if (!userId) {
            throw new Error('User ID is required');
        }
        
        const result = await updateFlashcard(flashcardId, updateData, userId);
        return result;
        
    } catch (error) {
        throw error;
    }
}
