import { apiResponse } from "../../../../utils/apiResponse.js";
import { InvalidInputValueError } from "../../../../utils/customErrors/index.js";
import connectToDB from '../../../../infra/libs/mongodb/connect.js';
import disconnectFromDB from '../../../../infra/libs/mongodb/disconnect.js';

// Import flashcard handlers
import { handler as createFlashcardHandler } from "./createFlashcard.js";
import { handler as getSelfFlashcardsHandler } from "./getSelfFlashcards.js";
import { handler as batchAnswerFlashcardsHandler } from "./batchAnswerFlashcards.js";
import { handler as updateFlashcardHandler } from "./updateFlashcard.js";
import { handler as deleteFlashcardsHandler } from "./deleteFlashcards.js";

export async function handler(event) {
  await connectToDB();
  try {
    if (event.source === "serverless-plugin-warmup") {
      return Promise.resolve("Lambda warmed up!");
    }

    console.time("Flashcards Management Duration");
    const functions = {
      // Flashcard endpoints
      "/flashcard": {
        GET: getSelfFlashcardsHandler,
        POST: createFlashcardHandler,
        PATCH: updateFlashcardHandler,
        DELETE: deleteFlashcardsHandler
      },
      "/flashcard/self": {
        GET: getSelfFlashcardsHandler
      },
      "/flashcard/batch-answer": {
        POST: batchAnswerFlashcardsHandler
      }
    };

    const resource = event.resource;
    const method = event.method || event.httpMethod;

    if (!functions[resource] || !functions[resource][method]) {
      throw new InvalidInputValueError(`Interface not mapped! for relative endpoint ${resource} with method : ${method}`);
    }

    const response = await functions[resource][method](event);
    console.log({response});
    console.timeEnd("Flashcards Management Duration");
    disconnectFromDB();
    return apiResponse(201, {body: response});
  } catch (error) {
    disconnectFromDB();
    console.log("FlashcardsManagement", error, event);
    return Promise.resolve(
      apiResponse(error.code || 500, {
        body: {
          error: error.name || "UnexpectedError",
          message: error.message || error,
          formattedMessage:
            error.formattedMessage || "An unexpected error occurred.",
        },
      })
    );
  }
}
