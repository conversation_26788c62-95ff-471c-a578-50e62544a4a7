import mongoose from 'mongoose';
import FlashcardRepository from '../../repositories/flashcardRepository.js';
import DeckRepository from '../../repositories/deckRepository.js';
import { ValidationError, NotFoundError, AuthorizationError } from '../../../../utils/customErrors/index.js';

/**
 * Deletes multiple flashcards from all decks and then deletes the flashcards themselves.
 * @param {string[]} flashcardIds - Array of flashcard IDs to delete.
 * @param {string} userId - The ID of the user requesting the deletion.
 * @returns {Promise<Object>} - Result object with deletion information.
 */
async function deleteFlashcards(flashcardIds, userId) {
  try {
    // Validate input
    if (!flashcardIds || !Array.isArray(flashcardIds) || flashcardIds.length === 0) {
      throw new ValidationError('Flashcard IDs array is required and must not be empty.');
    }

    // Validate that all IDs are valid MongoDB ObjectIds
    const invalidIds = flashcardIds.filter(id => !mongoose.Types.ObjectId.isValid(id));
    if (invalidIds.length > 0) {
      throw new ValidationError(`Invalid flashcard IDs: ${invalidIds.join(', ')}`);
    }

    // Check if flashcards exist and user has permission to delete them
    const flashcards = await FlashcardRepository.findAll({
      filter: { _id: { $in: flashcardIds } },
      userId,
      getAll: true
    });

    if (flashcards.length === 0) {
      throw new NotFoundError('No flashcards found with the provided IDs that you own.');
    }

    // Check if all requested flashcards were found (some might not exist or not be owned by user)
    if (flashcards.length !== flashcardIds.length) {
      const foundIds = flashcards.map(f => f.id);
      const notFoundIds = flashcardIds.filter(id => !foundIds.includes(id));
      throw new NotFoundError(`Some flashcards were not found or you don't have permission to delete them: ${notFoundIds.join(', ')}`);
    }

    // Start transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Remove flashcards from all decks
      await DeckRepository.removeFlashcardsFromAllDecks({ flashcardIds, session });

      // Delete the flashcards and their statistics
      await FlashcardRepository.deleteMultiple({ flashcardIds, session });

      // Commit transaction
      await session.commitTransaction();

      return {
        success: true,
        deletedCount: flashcardIds.length,
        message: `Successfully deleted ${flashcardIds.length} flashcard(s).`
      };
    } catch (error) {
      // Rollback transaction on error
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  } catch (error) {
    if (error instanceof ValidationError || error instanceof NotFoundError || error instanceof AuthorizationError) {
      throw error;
    }
    throw new Error(`Error deleting flashcards: ${error.message}`);
  }
}

export default deleteFlashcards;
