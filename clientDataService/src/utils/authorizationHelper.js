import { AuthorizationError } from './customErrors/index.js';

/**
 * Verifica se o usuário tem permissões administrativas
 * @param {Object} authContext - Contexto de autorização do evento
 * @returns {Object} - Informações do usuário autenticado
 */
export function requireAdminRole(event) {
    const authContext = getAuthContext(event);

    if (!authContext || !authContext.role) {
        throw new AuthorizationError('Authentication required');
    }

    const { role, id } = authContext;

    if (role !== 'admin') {
        throw new AuthorizationError('Admin privileges required for this operation');
    }

    return { userId: id, role };
}

/**
 * Extrai o contexto de autorização do evento da API Gateway
 * @param {Object} event - Evento da API Gateway
 * @returns {Object} - Contexto de autorização
 */
function getAuthContext(event) {
    return event.requestContext?.authorizer || null;
}