#!/bin/bash

# Pre-deployment check script for serverless applications
# This script validates the environment and stack state before deployment

set -e

# Configuration
STACK_NAME="cds-service-dev"
PROFILE="atomize-dev"
REGION="us-east-2"
STAGE="${1:-dev}"

echo "🔍 Pre-deployment checks for $STACK_NAME..."

# Check AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed"
    exit 1
fi

# Check AWS credentials
echo "✅ Checking AWS credentials..."
if ! aws sts get-caller-identity --profile $PROFILE &> /dev/null; then
    echo "❌ AWS credentials not configured or invalid for profile: $PROFILE"
    exit 1
fi

# Check stack status
echo "✅ Checking CloudFormation stack status..."
STACK_STATUS=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --profile $PROFILE \
    --region $REGION \
    --query 'Stacks[0].StackStatus' \
    --output text 2>/dev/null || echo "STACK_NOT_EXISTS")

echo "Stack Status: $STACK_STATUS"

case $STACK_STATUS in
    "CREATE_COMPLETE"|"UPDATE_COMPLETE"|"STACK_NOT_EXISTS")
        echo "✅ Stack is in a deployable state"
        ;;
    "UPDATE_ROLLBACK_COMPLETE")
        echo "⚠️  Stack is in UPDATE_ROLLBACK_COMPLETE state - deployment may work but investigate previous failure"
        ;;
    "UPDATE_ROLLBACK_FAILED"|"UPDATE_FAILED"|"CREATE_FAILED")
        echo "❌ Stack is in a failed state: $STACK_STATUS"
        echo "🔧 Run: aws cloudformation delete-stack --stack-name $STACK_NAME --profile $PROFILE --region $REGION"
        exit 1
        ;;
    "UPDATE_IN_PROGRESS"|"CREATE_IN_PROGRESS"|"DELETE_IN_PROGRESS")
        echo "❌ Stack operation in progress: $STACK_STATUS"
        echo "⏳ Wait for current operation to complete"
        exit 1
        ;;
    *)
        echo "⚠️  Unknown stack status: $STACK_STATUS"
        echo "🔍 Manual investigation required"
        exit 1
        ;;
esac

# Check Node.js version
echo "✅ Checking Node.js version..."
NODE_VERSION=$(node --version)
echo "Node.js version: $NODE_VERSION"

# Check npm dependencies
echo "✅ Checking npm dependencies..."
if [ ! -d "node_modules" ]; then
    echo "⚠️  node_modules not found - running npm install..."
    npm install
fi

# Check serverless framework
echo "✅ Checking Serverless Framework..."
if ! command -v serverless &> /dev/null; then
    echo "❌ Serverless Framework not installed"
    echo "🔧 Run: npm install -g serverless"
    exit 1
fi

# Validate serverless.yml
echo "✅ Validating serverless.yml..."
if ! serverless print --stage $STAGE &> /dev/null; then
    echo "❌ serverless.yml validation failed"
    exit 1
fi

# Check for common issues
echo "✅ Checking for common deployment issues..."

# Check if deployment bucket exists
DEPLOYMENT_BUCKET="cds-service-$STAGE-deployment-bucket"
if aws s3 ls "s3://$DEPLOYMENT_BUCKET" --profile $PROFILE --region $REGION &> /dev/null; then
    echo "✅ Deployment bucket exists: $DEPLOYMENT_BUCKET"
else
    echo "⚠️  Deployment bucket may not exist: $DEPLOYMENT_BUCKET"
fi

echo ""
echo "🎉 All pre-deployment checks passed!"
echo "🚀 Ready to deploy with: serverless deploy --stage $STAGE --verbose"
echo ""
