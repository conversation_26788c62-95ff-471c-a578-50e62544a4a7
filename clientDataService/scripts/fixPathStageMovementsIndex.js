// import mongoose from 'mongoose';
// import connectToDB from '../src/infra/libs/mongodb/connect.js';

// async function fixPathStageMovementsIndex() {
//   try {
//     console.log('Connecting to MongoDB...');
//     await connectToDB();

//     const db = mongoose.connection.db;
//     const collection = db.collection('pathstagemovements');

//     console.log('Checking existing indexes...');
//     const indexes = await collection.indexes();
//     console.log('Current indexes:', indexes.map(idx => idx.name));

//     // Find the old index (stageSourceId)
//     const oldIndex = indexes.find(idx =>
//       idx.name === 'userId_1_pathId_1_stageSourceId_1' ||
//       (idx.key && idx.key.userId && idx.key.pathId && idx.key.stageSourceId)
//     );

//     if (oldIndex) {
//       console.log('Dropping old index:', oldIndex.name);
//       await collection.dropIndex(oldIndex.name);
//       console.log('Old index dropped successfully');
//     } else {
//       console.log('Old index not found, proceeding to create new index');
//     }

//     // Check if new index already exists (stageOrder)
//     const newIndexExists = indexes.find(idx =>
//       idx.name === 'userId_1_pathId_1_stageOrder_1' ||
//       (idx.key && idx.key.userId && idx.key.pathId && idx.key.stageOrder)
//     );

//     if (!newIndexExists) {
//       console.log('Creating new index: { userId: 1, pathId: 1, stageOrder: 1 }');
//       await collection.createIndex(
//         { userId: 1, pathId: 1, stageOrder: 1 },
//         { unique: true }
//       );
//       console.log('New index created successfully');
//     } else {
//       console.log('New index already exists');
//     }

//     // Verify the new index
//     const updatedIndexes = await collection.indexes();
//     console.log('Updated indexes:', updatedIndexes.map(idx => idx.name));

//     console.log('Index migration completed successfully!');
//   } catch (error) {
//     console.error('Error fixing index:', error);
//     throw error;
//   } finally {
//     await mongoose.disconnect();
//     console.log('Disconnected from MongoDB');
//   }
// }

// // Run the migration
// fixPathStageMovementsIndex()
//   .then(() => {
//     console.log('Migration completed successfully');
//     process.exit(0);
//   })
//   .catch((error) => {
//     console.error('Migration failed:', error);
//     process.exit(1);
//   }); 