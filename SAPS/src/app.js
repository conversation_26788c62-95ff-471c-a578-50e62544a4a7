import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import VideoRouter from './presentation/routes/videoRouter.js';
import TeacherRouter from './presentation/routes/teacherRouter.js';
import PlaylistRouter from './presentation/routes/playlistRouter.js';

const app = express();
app.use(bodyParser.json());
app.use(cors())

const baseRoutePath = '/SAPS';

app.use((req, res, next) => {
    if (req.url.startsWith(baseRoutePath)) {
        req.url = req.url.slice(baseRoutePath.length);
    }
    next();
});

app.use('/video', VideoRouter);
app.use('/teacher', TeacherRouter);
app.use('/playlist', PlaylistRouter);

app.get('/hello', function (req, res) {
    res.send('Hello World!')
})


export default app;

const port = 4000; // Replace with your desired port number
app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
});