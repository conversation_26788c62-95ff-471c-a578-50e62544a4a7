class TeacherAggregateRoot {
    constructor({id, name, description, awards, videos, userId = null }) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.awards = awards;
        this.videos = videos;
        this.userId = userId;
    }

    static fromData({id, name, description, awards, videos, userId = null }) {
        return new TeacherAggregateRoot({id, name, description, awards, videos, userId });
    }
}

export default TeacherAggregateRoot;