import PlaylistVideo from "../models/playlistVideoModel.js";
import ValidSubject from "../valueObjects/validSubjects.js";

class PlaylistAggregateRoot {
    constructor({ id, title, subject = null, type, ownerId, videos, olympiads, levelRange }) {
        this.id = id;
        this.title = title;
        this.subject = subject;
        this.type = type;
        this.ownerId = ownerId;
        this.videos = videos;
        this.olympiads = olympiads;
        this.levelRange = levelRange;
    }

    static fromData({ id, title, subject = null, type, ownerId, videos = [], olympiads, levelRange = [] }) {
        if (ownerId == null)
            ownerId = "ATOMIZE";

        return new PlaylistAggregateRoot({ id, title, subject, type, ownerId, videos, olympiads, levelRange });
    }

    updateVideos({ videosIds }) {
        const videosToAdd = [];
        const videosToDeleteIds = [];
        const videosToUpdate = [];
        const currentVideosIds = this.videos.map(e => e.videoId);

        videosIds.forEach((vid, index) => {
            if (!currentVideosIds.includes(vid)) {
                videosToAdd.push(PlaylistVideo.create({
                    order: index + 1,
                    playlistId: this.id,
                    videoId: vid

                }));
            }
            else if (this.videos.find(v => v.videoId == vid && v.order != index + 1))
                videosToUpdate.push(PlaylistVideo.create({
                    order: index + 1,
                    playlistId: this.id,
                    videoId: vid

                }))
        });

        currentVideosIds.forEach(vid => {
            if (!videosIds.includes(vid))
                videosToDeleteIds.push(vid)
        });

        return { id: this.id, videosToAdd, videosToUpdate, videosToDeleteIds };
    }

    static validateFilter(filter) {
        if (!filter)
            throw new Error("Filter not defined")
        const allowedFields = ['title', 'subject', 'olympiads', 'levelRange', 'type', 'ownerId'];
        let levelRange = null;

        if (filter['olympiads'])
            filter['olympiads'].forEach((oId, index) => filter['olympiads'][index] = oId.toString())

        console.log(filter)
        // Parse data
        if (filter['type']) {
            const allowedTypes = ['GLOBAL', 'LOCAL', 'PRIVATE', 'LIKED', 'SAVED'];
            if (!allowedTypes.includes(filter['type'])) {
                throw new Error(`Invalid type: ${filter['type']}`);
            }
        }

        const parsedFilters = [];

        for (const key in filter) {
            if (!allowedFields.includes(key)) {
                throw new Error(`Invalid filter field: ${key}`);
            }

            if (key === 'title' || key === 'ownerId') {
                if (typeof filter[key] !== 'string' || filter[key].length > 255) {
                    throw new Error(`Invalid value for field: ${key}`);
                }
                parsedFilters.push({
                    [key]: {
                        contains: filter[key],
                        mode: 'insensitive'
                    }
                });
                continue;
            }

            if (key === 'subject' || key === 'olympiads') {
                if (!Array.isArray(filter[key])) {
                    throw new Error(`Invalid value for field: ${key} - expected an array`);
                }
                parsedFilters.push({
                    [key]: {
                        hasEvery: filter[key]
                    }
                });
                continue;
            }

            if (key === 'levelRange') {
                if (levelRange.length === 1)
                    parsedFilters.push({
                        levelRange: {
                            has: filter[key][0] // Includes single value in db levelRange array
                        }
                    });
                else
                    levelRange = filter[key];
                continue;
            }

            parsedFilters.push({
                [key]: filter[key]
            });
        }

        console.log(parsedFilters);
        return { queryFilters: { AND: parsedFilters }, serverFilters: { levelRange } };
    }

}



export default PlaylistAggregateRoot;