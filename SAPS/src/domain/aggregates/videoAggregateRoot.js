class VideoAggregateRoot {
    constructor({ title, teacherId, videoUrl, subject, id, likes, comments, savedBy, thumbnail, videoFiles,tags, teacher }) {
        this.title = title;
        this.teacherId = teacherId;
        this.videoUrl = videoUrl;
        this.subject = subject;
        this.id = id;
        this.likes = likes;
        this.comments = comments;
        this.savedBy = savedBy;
        this.thumbnail = thumbnail;
        this.videoFiles = videoFiles;
        this.tags = tags;
        this.teacher = teacher;
    }

    async addSave(userId) {
        const newSaving = { userId, videoId: this.id }

        return newSaving;
    }

    async addLike(userId) {
        const newLike = { userId, videoId: this.id }
        this.likes.push(newLike);
        return newLike;
    }

    async addComment({ content, isDoubt, userId, username }) {
        const newComment = { userId, username, content, isDoubt, videoId: this.id }
        this.comments.push(newComment);
        return newComment;
    }

    async addCommentToComment({ commentId, content, userId, username }) {
        const newComment = { userId, username, content, parentCommentId: commentId, videoId: this.id };
        this.comments.push(newComment);
        return newComment;
    }

    async addCommentLike({ commentId, userId }) {
        const newCommentLike = { userId, commentId };

        return newCommentLike;
    }

    async getComments() {
        return this.comments;
    }

    async getLikes() {
        return this.likes;
    }


    static fromData({ title, teacherId, videoUrl, subject, id, likes, comments,thumbnail, videoFiles,description,tags, teacher }) {
        return new VideoAggregateRoot({ title, teacherId, videoUrl, subject, id, likes, comments, thumbnail, videoFiles,description,tags, teacher });
    }
}

export default VideoAggregateRoot;
