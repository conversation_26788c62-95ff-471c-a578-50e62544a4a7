import { PrismaClient } from '../models/generated/client';
import PlaylistAggregateRoot from '../aggregates/playlistAggregateRoot.js';
const prisma = new PrismaClient();

class PlaylistRepository {
    async findById(playlistId) {
        const playlistData = await prisma.playlist.findUnique({
            where: { id: playlistId },
            include: {
                PlaylistVideo: {
                    include: {
                        video: true
                    },
                    orderBy: {
                        order: 'asc'
                    }
                }
            }
        });

        if (!playlistData) {
            throw new Error('Playlist not found');
        }

        return PlaylistAggregateRoot.fromData({ ...playlistData, videos: playlistData.PlaylistVideo });
    }

    static async findByFilter({ queryFilters }) {
        const playlistsData = await prisma.playlist.findMany({
            where: queryFilters,
            include: {
                PlaylistVideo: {
                    include: {
                        video: true
                    },
                    orderBy: {
                        order: 'asc'
                    }
                }
            }
        });

        return playlistsData.map(playlistData =>
            PlaylistAggregateRoot.fromData({ ...playlistData, videos: playlistData.PlaylistVideo })
        );
    }

    static async findSingleByFilter({ queryFilters }) {
        const playlistData = await prisma.playlist.findFirst({
            where: queryFilters,
            include: {
                PlaylistVideo: {
                    include: {
                        video: true
                    },
                    orderBy: {
                        order: 'asc'
                    }
                }
            }
        });

        if (!playlistData) {
            throw new Error('Playlist not found');
        }

        return PlaylistAggregateRoot.fromData({ ...playlistData, videos: playlistData.PlaylistVideo });
    }

    async getByOwner({ page = 1, limit = 10, ownerId, subject = null }) {
        const skip = (page - 1) * limit;
        let playlistData = [];

        if (!!subject) {
            playlistData = await prisma.playlist.findMany({
                skip,
                take: limit,
                where: {
                    ownerId: ownerId,
                    subject: subject
                }
            });
        } else {
            playlistData = await prisma.playlist.findMany({
                skip,
                take: limit,
                where: {
                    ownerId: ownerId
                }
            });
        }

        return playlistData;
    }

    async getByType({ page = 1, limit = 10, type, subject, ownerId }) {
        const skip = (page - 1) * limit;
        let playlistsData = [];
        if (subject) {
            playlistsData = await prisma.playlist.findMany({
                skip,
                take: limit,
                where: {
                    OR: [
                        {
                            ownerId: ownerId,
                            subject: subject,
                            type: type
                        },
                        {
                            subject: subject,
                            type: 'GLOBAL'
                        }
                    ]
                },
                include: {
                    PlaylistVideo: {
                        include: {
                            video: true
                        }
                    }
                }
            });
        }
        else {
            playlistsData = await prisma.playlist.findMany({
                skip,
                take: limit,
                where: {
                    OR: [
                        {
                            ownerId: ownerId,
                            type: type
                        },
                        {
                            type: "GLOBAL"
                        }
                    ]
                },
                include: {
                    PlaylistVideo: {
                        include: {
                            video: true
                        }
                    }
                }
            });
        }

        return playlistsData;
    }

    async create(playlistData) {
        const { videos, ...playlistDetails } = playlistData;

        const transformedData = {
            ...playlistDetails,

        };

        const newPlaylist = await prisma.playlist.create({
            data: transformedData
        });

        return newPlaylist;
    }

    async update(playlistData) {
        const updatedPlaylist = await prisma.playlist.update({
            where: { id: playlistData.id },
            data: { ...playlistData }
        });
        return updatedPlaylist;
    }

    async updateVideos({ playlistId, videosToAdd, videosToUpdate, videosToDeleteIds }) {
        const { videosAdded, videosUpdated, videosDeleted } = await prisma.$transaction(async (tsPrisma) => {

            let videosAdded = []
            let videosUpdated = []
            let videosDeleted = []

            if (videosToAdd.length > 0) {
                videosAdded = await tsPrisma.playlistVideo.createMany({
                    data: videosToAdd
                })
            }

            if (videosToUpdate.length > 0) {
                await Promise.all(videosToUpdate.map(async videoPlaylist => {
                    videosUpdated = await tsPrisma.playlistVideo.update({
                        where: { playlistId_videoId: { playlistId, videoId: videoPlaylist.videoId } },
                        data: { ...videoPlaylist }
                    })
                }))
            }

            if (videosToDeleteIds.length > 0) {
                videosDeleted = await Promise.all(videosToDeleteIds.map(async videoId => {
                    await tsPrisma.playlistVideo.delete({
                        where: { playlistId_videoId: { playlistId, videoId } }
                    })
                }))
            }
            return { videosAdded, videosUpdated, videosDeleted }
        })

        return { videosAdded, videosToAdd, videosUpdated, videosDeleted }
    }

    async getPaginated(page = 0, limit = 10) {
        const skip = (page) * limit;
        const playlistData = await prisma.playlist.findMany({
            skip,
            take: limit,
            include: {
                PlaylistVideo: true
            }
        });

        return playlistData;
    }

    async delete(playlistId) {
        const deletedPlaylist = await prisma.playlist.delete({
            where: { id: playlistId }
        });
        return deletedPlaylist;
    }
}

export default PlaylistRepository;
