import { PrismaClient } from '../models/generated/client';
import TeacherAggregateRoot from '../aggregates/teacherAggregateRoot.js';

const prisma = new PrismaClient();

class TeacherRepository {
    async findById(teacherId) {
        const teacherData = await prisma.teacher.findUnique({
            where: { id: teacherId },
            include: {
                videos: true
            }
        });

        if (!teacherData) {
            throw new Error('Teacher not found');
        }

        return TeacherAggregateRoot.fromData(teacherData);
    }

    async create(teacherData) {
        const cleanedTeacherData = Object.fromEntries(
            Object.entries(teacherData).filter(([_, v]) => v !== undefined && v !== null)
          );
          
        const newTeacher = await prisma.teacher.create({
            data: { ...cleanedTeacherData }
        });

        return newTeacher;
    }

    async update(teacherData) {
        const updatedTeacher = await prisma.teacher.update({
            where: { id: teacherData.id },
            data: { ...teacherData }
        });

        return updatedTeacher;
    }

    async getPaginatedTeachers(page = 0, limit = 10) {
        const skip = (page) * limit;
        const teacherData = await prisma.teacher.findMany({
            skip,
            take: limit,
            include: {
                videos: true
            }
        });

        return teacherData.map(teacherData => TeacherAggregateRoot.fromData(teacherData));
    }
}

export default TeacherRepository;
