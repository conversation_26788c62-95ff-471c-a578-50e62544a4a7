/*
  Warnings:

  - A unique constraint covering the columns `[title,ownerId]` on the table `Playlist` will be added. If there are existing duplicate values, this will fail.
  - Changed the type of `type` on the `Playlist` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "PlaylistType" AS ENUM ('GLOBAL', 'LOCAL', 'PRIVATE', 'LIKED', 'SAVED');

-- AlterTable
ALTER TABLE "Playlist" ADD COLUMN     "ownerId" TEXT NOT NULL DEFAULT 'atomize',
ALTER COLUMN "subject" DROP NOT NULL,
DROP COLUMN "type",
ADD COLUMN     "type" "PlaylistType" NOT NULL;

-- CreateTable
CREATE TABLE "VideoSaved" (
    "id" SERIAL NOT NULL,
    "userId" TEXT NOT NULL,
    "videoId" INTEGER NOT NULL,

    CONSTRAINT "VideoSaved_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "VideoSaved_videoId_userId_key" ON "VideoSaved"("videoId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "Playlist_title_ownerId_key" ON "Playlist"("title", "ownerId");

-- AddForeignKey
ALTER TABLE "VideoSaved" ADD CONSTRAINT "VideoSaved_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "Video"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
