/*
  Warnings:

  - You are about to drop the `_PlaylistToVideo` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "_PlaylistToVideo" DROP CONSTRAINT "_PlaylistToVideo_A_fkey";

-- DropForeignKey
ALTER TABLE "_PlaylistToVideo" DROP CONSTRAINT "_PlaylistToVideo_B_fkey";

-- DropTable
DROP TABLE "_PlaylistToVideo";

-- CreateTable
CREATE TABLE "_VideoPlaylists" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_VideoPlaylists_AB_unique" ON "_VideoPlaylists"("A", "B");

-- CreateIndex
CREATE INDEX "_VideoPlaylists_B_index" ON "_VideoPlaylists"("B");

-- AddForeignKey
ALTER TABLE "_VideoPlaylists" ADD CONSTRAINT "_VideoPlaylists_A_fkey" FOREIGN KEY ("A") REFERENCES "Playlist"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_VideoPlaylists" ADD CONSTRAINT "_VideoPlaylists_B_fkey" FOREIGN KEY ("B") REFERENCES "Video"("id") ON DELETE CASCADE ON UPDATE CASCADE;
