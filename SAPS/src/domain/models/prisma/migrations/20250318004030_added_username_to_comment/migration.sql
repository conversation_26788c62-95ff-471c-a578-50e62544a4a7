-- AlterTable
ALTER TABLE "Comment" ADD COLUMN     "username" TEXT NOT NULL DEFAULT 'Anonymous';

-- AlterTable
ALTER TABLE "Playlist" ADD COLUMN     "levelRange" INTEGER[];

-- AlterTable
ALTER TABLE "Video" ADD COLUMN     "description" TEXT NOT NULL DEFAULT '',
ADD COLUMN     "tags" TEXT[];

-- CreateTable
CREATE TABLE "VideoWatched" (
    "id" SERIAL NOT NULL,
    "userId" TEXT NOT NULL,
    "videoId" INTEGER NOT NULL,
    "watchedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "VideoWatched_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "VideoWatched_videoId_userId_key" ON "VideoWatched"("videoId", "userId");

-- AddForeignKey
ALTER TABLE "VideoWatched" ADD CONSTRAINT "VideoWatched_videoId_fkey" FOREIGN KEY ("videoId") REFERENCES "Video"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
