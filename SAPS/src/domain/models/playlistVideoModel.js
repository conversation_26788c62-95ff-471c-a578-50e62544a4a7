export default class PlaylistVideo {
    constructor({ id, playlistId, videoId, order }) {
        this.id = id;
        this.playlistId = playlistId;
        this.videoId = videoId;
        this.order = order;
    }

    static create({ id, playlistId, videoId, order }) {
        if (!playlistId || !videoId || !order) {
            throw Error("Invalid parameters");
            
        }
        return new PlaylistVideo({ id, playlistId, videoId, order });
    }
}   