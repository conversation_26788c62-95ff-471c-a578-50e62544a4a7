import { getPlaylistByTypeService } from "../../../application/services/playlist/getPlaylistByType.js";
import { apiResponse } from "../../../../utils/apiResponse.js";
export async function handler(event) {
  try {
    const { type } = event.pathParameters;
    const page = parseInt(event.queryStringParameters?.page) || 1;
    const limit = parseInt(event.queryStringParameters?.limit) || 10;
    const subject = event.queryStringParameters?.subject;
    console.log("type", type);
    console.log("subject", subject);
    console.log("page", page);
    console.log("limit", limit);

    const result = await getPlaylistByTypeService({ page, limit, type, subject });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in getPlaylistByType handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 