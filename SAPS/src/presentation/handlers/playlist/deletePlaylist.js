import deletePlaylistService from "../../../application/services/playlist/deletePlaylistService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const { playlistId } = event.pathParameters;
    const result = await deletePlaylistService({ playlistId: parseInt(playlistId) });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in deletePlaylist handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 