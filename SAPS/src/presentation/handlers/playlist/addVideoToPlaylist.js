import addVideoToPlaylistService from "../../../application/services/playlist/addVideoToPlaylistService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const body = JSON.parse(event.body);
    const { playlistId, videosIds } = body;

    const result = await addVideoToPlaylistService({
      playlistId: playlistId,
      videosIds: videosIds,
    });

    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in addVideoToPlaylist handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
}
