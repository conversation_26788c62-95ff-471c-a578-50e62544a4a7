import { updatePlaylistService } from "../../../application/services/playlist/updatePlaylistService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const { playlistId } = event.pathParameters;
    const body = JSON.parse(event.body);
    const { title, subject, type } = body;

    const result = await updatePlaylistService({ playlistId, title, subject, type });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in updatePlaylist handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 