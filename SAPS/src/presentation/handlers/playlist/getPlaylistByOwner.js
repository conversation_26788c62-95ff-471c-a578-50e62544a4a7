import { getPlaylistByOwnerService } from "../../../application/services/playlist/getPlaylistByOwner.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const { ownerId } = event.pathParameters;
    const body = JSON.parse(event.body);
    const { subject } = body;
    const page = parseInt(event.queryStringParameters?.page) || 1;
    const limit = parseInt(event.queryStringParameters?.limit) || 10;

    const result = await getPlaylistByOwnerService({ page, limit, ownerId, subject });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in getPlaylistByOwner handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 