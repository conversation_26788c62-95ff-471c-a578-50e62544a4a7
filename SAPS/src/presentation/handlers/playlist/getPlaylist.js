import getPlaylistService from "../../../application/services/playlist/getPlaylistService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const playlistId = parseInt(event.pathParameters.playlistId);
    const result = await getPlaylistService({ playlistId });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in getPlaylist handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 