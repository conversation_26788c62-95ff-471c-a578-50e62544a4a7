import getSinglePlaylistByFilterService from "../../../application/services/playlist/getSinglePlaylistByFilterService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    let { filter } = event.queryStringParameters;
    console.log("filter", filter);
    if (typeof filter === "string") {
      // Parse the filter string to an object
      filter = JSON.parse(filter);
    }

    for (const key in filter) {
      if (typeof filter[key] === "string") {
        // Try to parse the string; if it's a valid JSON array representation, it will succeed
        const parsedValue = JSON.parse(filter[key]);
        // Check if the parsed value is an array
        if (Array.isArray(parsedValue)) {
          filter[key] = parsedValue; // Replace with the parsed array
        }
      }
    }
    const result = await getSinglePlaylistByFilterService(filter);
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log("Error in getSinglePlaylistByFilter handler:", error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
}
