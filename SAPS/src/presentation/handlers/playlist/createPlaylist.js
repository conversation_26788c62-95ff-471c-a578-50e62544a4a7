import { createPlaylistService } from "../../../application/services/playlist/createPlaylistService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const body = JSON.parse(event.body);
    const result = await createPlaylistService({ ...body });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in createPlaylist handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
}
