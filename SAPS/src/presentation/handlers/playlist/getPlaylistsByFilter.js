import getPlaylistsByFilterService from "../../../application/services/playlist/getPlaylistsByFilter.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    let { filter } = event.queryStringParameters;

    // Check if filter is a string and parse it to an object
    if (typeof filter === "string") {
      filter = JSON.parse(filter);
    }

    // Parse any string arrays in the filter
    for (const key in filter) {
      if (typeof filter[key] === "string") {
        const parsedValue = JSON.parse(filter[key]);
        if (Array.isArray(parsedValue)) {
          filter[key] = parsedValue;
        }
      }
    }

    const result = await getPlaylistsByFilterService(filter);
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in getPlaylistsByFilter handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
}
