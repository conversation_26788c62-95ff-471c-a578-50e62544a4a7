import { createTeacherService } from "../../../application/services/teacher/createTeacherService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const body = JSON.parse(event.body);
    const result = await createTeacherService({ ...body });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in createTeacher handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 