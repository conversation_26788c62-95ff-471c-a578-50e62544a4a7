import { updateTeacherService } from "../../../application/services/teacher/updateTeacherService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const { id } = event.pathParameters;
    const body = JSON.parse(event.body);
    const { name, description, awards } = body;

    const result = await updateTeacherService({ id: parseInt(id), name, description, awards });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in updateTeacher handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 