// lambda-authorizer.js
import jwt from "jsonwebtoken"

export async function handler (event) {
    console.log("Event on authorizer middleware", event);
    // bearer token
    const token = event.authorizationToken?.split(' ')[1]; // Get the token from the request, removing "Bearer" prefix
    const secret = process.env.JWT_SECRET; // Use the JWT_SECRET from environment variables

    try {
        const decoded = jwt.verify(token, secret);
        console.log("decoded", decoded);
        // Pass id and role from the JWT payload
        return generatePolicy(decoded.sub, 'Allow', event.methodArn, { id: decoded.id, role: decoded.role });
    } catch (err) {
        console.error("JWT verification failed", err);
        throw new Error("Unauthorized"); // Throw an error for unauthorized access
    }
};

const generatePolicy = (principalId, effect, resource, context) => {
    const authResponse = {};
    authResponse.principalId = principalId;
    if (effect && resource) {
        const policyDocument = {
            Version: '2012-10-17',
            Statement: [{
                Action: 'execute-api:Invoke',
                Effect: "Allow",
                Resource: "*",
            }],
        };
        authResponse.policyDocument = policyDocument;
    }
    if (context) {
        authResponse.context = context; // Add context to the response
    }
    return authResponse;
};