// Handler para buscar informações de comentário para o CDS
import VideoRepository from '../../../domain/repositories/videoRepository.js';
import { NotFoundError } from '../../../application/customErrors/index.js';
import { apiResponse } from '../../../../utils/apiResponse.js';

const videoRepository = new VideoRepository();

export async function handler(event) {
    try {
        const { commentId } = event.pathParameters;
        
        if (!commentId) {
            return apiResponse(400, { body: { message: 'CommentId is required' } });
        }

        // Buscar informações do comentário
        const commentInfo = await videoRepository.getCommentInfo(commentId);
        
        if (!commentInfo) {
            return apiResponse(404, { body: { message: 'Comment not found' } });
        }

        return apiResponse(200, { body: commentInfo });
    } catch (error) {
        console.error('Error getting comment info:', error);
        return apiResponse(500, { body: { message: error.message } });
    }
}