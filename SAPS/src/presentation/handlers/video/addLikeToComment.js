import { addLikeToCommentService } from "../../../application/services/video/addLikeToComment.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const body = JSON.parse(event.body);
    const result = await addLikeToCommentService({ ...body, token: event.headers.Authorization });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log(error)   
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 