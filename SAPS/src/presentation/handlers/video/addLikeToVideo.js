import { addLikeToVideoService } from "../../../application/services/video/addLikeToVideoService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const body = JSON.parse(event.body);
    const { userId, videoId, playlistId } = body;
    console.log(userId, videoId, playlistId)
    const result = await addLikeToVideoService({ userId, videoId, playlistId, token: event.headers.Authorization });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log(error)   
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 