import addWatchedToVideoService from '../../../application/services/video/addWatchedToVideoService.js';
import { apiResponse } from '../../../../utils/apiResponse.js';

export const handler = async (event) => {
    const body = JSON.parse(event.body);
    const { videoId, playlistId, userId } = body;
    try {
        const isWatched = await addWatchedToVideoService({ videoId, playlistId, userId , token: event.headers.Authorization});
        return apiResponse(200, { body: isWatched });
    } catch (error) {
        return apiResponse(error.statusCode || 500, { body: error });
    }
};