import { removeVideoLikeService } from "../../../application/services/video/removeVideoLikeService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const body = JSON.parse(event.body);
    const { videoId, userId } = body;
    const result = await removeVideoLikeService({ videoId, userId });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in removeVideoLike handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 