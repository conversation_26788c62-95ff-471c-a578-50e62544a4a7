import getVideoByIdService from "../../../application/services/video/getVideoById.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const { id } = event.pathParameters;
    const result = await getVideoByIdService(parseInt(id));
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log(error)    
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 