import { removeVideoSaveService } from "../../../application/services/video/removeVideoSaveService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const body = JSON.parse(event.body);
    const { videoId, userId } = body;
    const result = await removeVideoSaveService({ videoId, userId });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in removeVideoSave handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 