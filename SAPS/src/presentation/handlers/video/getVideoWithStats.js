import getVideoWithStatsService from "../../../application/services/video/getVideoWithStatsService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const { id } = event.pathParameters;
    const { videoId, playlistId } = event.queryStringParameters || {};
    console.log("ATHANDLER",{id, videoId, playlistId})

    // Validate required parameters
    if (!videoId) {
      return apiResponse(400, { body: { error: "videoId is required" } });
    }

    const parsedVideoId = parseInt(videoId);
    if (isNaN(parsedVideoId)) {
      return apiResponse(400, { body: { error: "videoId must be a valid number" } });
    }

    const result = await getVideoWithStatsService({ 
      userId: id, 
      videoId: parsedVideoId, 
      playlistId: playlistId ? parseInt(playlistId) : undefined,
      token: event.headers.Authorization
    });
    console.log("RESULT", result)
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in getVideoWithStats handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 