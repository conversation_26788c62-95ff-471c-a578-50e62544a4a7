import { removeCommentLikeService } from "../../../application/services/video/removeCommentLikeService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const body = JSON.parse(event.body);
    const { commentId, userId } = body;
    const result = await removeCommentLikeService({ commentId, userId });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in removeCommentLike handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 