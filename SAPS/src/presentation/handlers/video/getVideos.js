import { getVideosService } from "../../../application/services/video/getVideosService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const page = parseInt(event.queryStringParameters?.page) || 1;
    const limit = parseInt(event.queryStringParameters?.limit) || 10;

    const result = await getVideosService(page, limit);
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log(error); 
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 