import { deleteVideoService } from "../../../application/services/video/deleteVideoService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const body = JSON.parse(event.body);
    const { videoId } = body;
    const result = await deleteVideoService(videoId);
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log(error)   
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 