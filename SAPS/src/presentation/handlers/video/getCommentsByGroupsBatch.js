import { getCommentsByGroupsBatchService } from "../../../application/services/video/getCommentsByGroupsBatchService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

/**
 * Batch handler for getting comments from multiple groups
 * This reduces the number of API calls from N to 1
 */
export async function handler(event) {
  try {
    console.log("getCommentsByGroupsBatch handler triggered with event:", event);
    
    const authHeader = event.headers?.Authorization || event.headers?.authorization;
    if (!authHeader) {
      return apiResponse(401, { body: { message: 'Missing Authorization header' } });
    }

    if (!event.body) {
      return apiResponse(400, { body: { message: 'Missing request body' } });
    }

    const body = JSON.parse(event.body);
    const { groupIds, videoId, page = 1, limit = 20 } = body;

    // Validate input
    if (!groupIds || !Array.isArray(groupIds) || groupIds.length === 0) {
      return apiResponse(400, { body: { message: 'groupIds array is required and cannot be empty' } });
    }

    if (groupIds.length > 50) {
      return apiResponse(400, { body: { message: 'Maximum 50 groups allowed per batch request' } });
    }

    console.log(`Processing batch request for ${groupIds.length} groups:`, {
      groupIds: groupIds.slice(0, 5), // Log first 5 for debugging
      videoId,
      page,
      limit,
      totalGroups: groupIds.length
    });

    const result = await getCommentsByGroupsBatchService({
      groupIds,
      videoId,
      isDoubt: true, // This endpoint is specifically for doubts
      page,
      limit,
      token: authHeader
    });

    console.log('getCommentsByGroupsBatch success summary', {
      totalComments: result.comments?.length || 0,
      groupResults: result.groupResults?.length || 0,
      successfulGroups: result.groupResults?.filter(g => g.success).length || 0
    });

    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in getCommentsByGroupsBatch handler:', { 
      message: error.message, 
      stack: error.stack, 
      name: error.name, 
      status: error.status 
    });
    
    const status = error.status || error.statusCode || 500;
    return apiResponse(status, { 
      body: { 
        message: error.message || 'Internal server error', 
        upstream: error.service 
      } 
    });
  }
}
