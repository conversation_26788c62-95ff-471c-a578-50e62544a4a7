import { createVideoService } from "../../../application/services/video/createVideoService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const body = JSON.parse(event.body);
    const { title, teacherId, videoUrl, subject, thumbnail, description, tags } = body;
    const result = await createVideoService({ title, teacherId, videoUrl, subject, thumbnail, description, tags });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log(error)   
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 