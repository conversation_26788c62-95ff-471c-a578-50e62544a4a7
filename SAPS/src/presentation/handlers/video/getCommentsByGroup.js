import { getCommentsByGroupService } from "../../../application/services/video/getCommentsByGroupService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    console.log("getCommentsByGroup handler triggered with event:", event);
    
  const { groupId } = event.pathParameters || {};
    
    // Get query parameters
    const videoId = event.queryStringParameters?.videoId;
    const isDoubt = event.queryStringParameters?.isDoubt ? 
      event.queryStringParameters.isDoubt === 'true' : undefined;
    const page = event.queryStringParameters?.page ? 
      parseInt(event.queryStringParameters.page) : 1;
    const limit = event.queryStringParameters?.limit ? 
      parseInt(event.queryStringParameters.limit) : 20;

    if (!groupId) {
      return apiResponse(400, { body: { message: "groupId is required" } });
    }

    const authHeader = event.headers?.Authorization || event.headers?.authorization;
    if (!authHeader) {
      return apiResponse(401, { body: { message: 'Missing Authorization header' } });
    }

    const result = await getCommentsByGroupService({
      groupId,
      videoId,
      isDoubt,
      page,
      limit,
      token: authHeader
    });
    console.log('getCommentsByGroup success summary', {
      hasResult: !!result,
      type: typeof result,
      keys: result && typeof result === 'object' ? Object.keys(result).slice(0, 10) : null
    });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in getCommentsByGroup handler:', { message: error.message, stack: error.stack, name: error.name, status: error.status });
    const status = error.status || error.statusCode || 500;
    return apiResponse(status, { body: { message: error.message || 'Internal server error', upstream: error.service } });
  }
}
