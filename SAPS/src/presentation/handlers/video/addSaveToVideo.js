import addSaveToVideoService from "../../../application/services/video/addSaveToVideoService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    console.log("event", event)
    const body = JSON.parse(event.body);
    const { userId, videoId } = body;
    const result = await addSaveToVideoService({ userId, videoId, token: event.headers.Authorization });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log(error)   
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 