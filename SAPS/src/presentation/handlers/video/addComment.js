import { addCommentToVideoService } from "../../../application/services/video/addCommentToVideoService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const authHeader = event.headers?.Authorization || event.headers?.authorization;
    if (!authHeader) {
      return apiResponse(401, { body: { message: 'Missing Authorization header' } });
    }
    if (!event.body) {
      return apiResponse(400, { body: { message: 'Missing body' } });
    }
    const body = JSON.parse(event.body);
    const result = await addCommentToVideoService({ ...body, token: authHeader });
    return apiResponse(201, { body: result });
  } catch (error) {
    console.log('addComment handler error', { message: error.message, stack: error.stack });
    return apiResponse(error.status || error.statusCode || 500, { body: { message: error.message || 'Internal server error' } });
  }
}