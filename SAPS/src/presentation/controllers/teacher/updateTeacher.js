import { updateTeacherService } from "../../../application/services/teacher/updateTeacherService.js";

export async function updateTeacherController(req, res) {
    try {
        const id = parseInt(req.params.id);
        const { name, description, awards} = req.body;
        const newTeacher = await updateTeacherService({ id, name, description, awards });
        res.status(200).send(newTeacher);
    } catch (error) {
        res.status(500).send("Server error");
    }
}