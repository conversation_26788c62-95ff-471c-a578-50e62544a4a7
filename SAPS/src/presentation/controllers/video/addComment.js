import { addCommentToVideoService } from '../../../application/services/video/addCommentToVideoService.js';
import { NotFoundError } from '../../../application/customErrors/index.js';

export async function addCommentToVideoController(req, res) {
    try {       
        const newComment = await addCommentToVideoService({...req.body});
        res.status(200).send(newComment);
    } catch (error) {
        if (error instanceof NotFoundError) {
            return res.status(404).send({ message: error.message });
        }
        res.status(500).send("Server error");
    }
}