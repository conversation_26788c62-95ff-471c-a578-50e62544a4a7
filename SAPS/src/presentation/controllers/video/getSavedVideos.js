import { getSavedVideosService } from "../../../application/services/video/getSavedVideosService.js";

export async function getSavedVideosController(req, res) {
    try {
        const page = parseInt(req.query.page) || 0;
        const limit = parseInt(req.query.limit) || 10;
        const userId = req.params.userId;

        if (!userId) {
            return res.status(400).send("User ID is required");
        }

        const savedVideos = await getSavedVideosService({ page, limit, userId });
        res.status(200).send(savedVideos);
    } catch (error) {
        res.status(500).send(error);
    }
}