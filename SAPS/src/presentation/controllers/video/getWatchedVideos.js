// não vamos utilizar esse controller por enquanto por nçao estar utilizando o BD relacional

// import { getWatchedVideosService } from "../../../application/services/video/getWatchedVideosService.js";

// export async function getWatchedVideosController(req, res) {
//     try {
//         const page = parseInt(req.query.page) || 0;
//         const limit = parseInt(req.query.limit) || 10;
//         const userId = req.params.userId;

//         if (!userId) {
//             return res.status(400).send("User ID is required");
//         }

//         const watchedVideos = await getWatchedVideosService({ page, limit, userId });
//         res.status(200).send(watchedVideos);
//     } catch (error) {
//         res.status(500).send(error);
//     }
// }