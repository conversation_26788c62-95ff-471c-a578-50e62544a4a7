import { removeVideoSaveService } from "../../../application/services/video/removeVideoSaveService.js";

export async function removeVideoSaveController(req, res) {
    try {
        const { videoId, userId } = req.body;

        const isDeleted = await removeVideoSaveService({ videoId, userId });
        res.status(200).send(isDeleted);
    } catch (error) {
        res.status(500).send("Server error");
    }
}