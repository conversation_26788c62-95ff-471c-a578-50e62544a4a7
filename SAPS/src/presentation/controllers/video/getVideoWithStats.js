import getVideoWithStatsService from "../../../application/services/video/getVideoWithStatsService.js";

export async function getVideoWithStatsController(req, res) {
    try {
        const id = req.params.id;
        const videoId = parseInt(req.query.videoId);
        const playlistId = parseInt(req.query.playlistId);

        const { statistics, userStatistics, video } = await getVideoWithStatsService({ userId: id, videoId: videoId, playlistId });

        res.status(200).send({ statistics, userStatistics, video });
    } catch (error) {
        console.log(error)
        res.status(500).send(error);
    }
}