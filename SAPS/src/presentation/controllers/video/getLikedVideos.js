import { getLikedVideosService } from "../../../application/services/video/getLikedVideosService.js";

export async function getLikedVideosController(req, res) {
    try {
        const page = parseInt(req.query.page) || 0;
        const limit = parseInt(req.query.limit) || 10;
        const userId = req.params.userId;
        if (!userId) {
            return res.status(400).send("User ID is required");
        }

        const likedVideos = await getLikedVideosService({page, limit, userId});
        res.status(200).send(likedVideos);
    } catch (error) {
        res.status(500).send(error);
    }
}