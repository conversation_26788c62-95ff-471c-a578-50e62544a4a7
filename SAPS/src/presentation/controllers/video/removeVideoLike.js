import { removeVideoLikeService } from "../../../application/services/video/removeVideoLikeService.js";

export async function removeVideoLikeController(req, res) {
    try {
        const { videoId, userId } = req.body;

        const isDeleted = await removeVideoLikeService({ videoId, userId });
        res.status(200).send(isDeleted);
    } catch (error) {
        res.status(500).send("Server error");
    }
}