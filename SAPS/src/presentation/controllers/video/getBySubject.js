import { getVideosBySubjectService } from "../../../application/services/video/getVideoBySubjectService.js";

export async function getVideosBySubjectController(req, res) {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const { subject } = req.params;

        if (!userId) {
            return res.status(400).send("User ID is required");
        }

        const videos = await getVideosBySubjectService({ subject, limit, page });
        res.status(200).send(videos);
    } catch (error) {
        res.status(500).send(error);
    }
}