import { customError } from '../../../application/customErrors/index.js';
import addWatchedToVideoService from '../../../application/services/video/addWatchedToVideoService.js';
export async function addWatchedToVideoController(req, res) {
    try {
        const { userId, playlistId, videoId } = req.body;
        const newWatched = await addWatchedToVideoService({ userId, playlistId, videoId });
        res.status(200).send(newWatched);
    } catch (error) {
        if (error instanceof customError) {
            return res.status(404).send({ message: error.message })
        }
        res.status(500).send("Server error");
    }
}