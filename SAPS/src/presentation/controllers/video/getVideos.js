import { getVideosService } from "../../../application/services/video/getVideosService.js";

export async function getVideosController(req, res) {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;

        const videos = await getVideosService(page, limit);
        res.status(200).send(videos);
    } catch (error) {
        res.status(500).send("Server error");
    }
}