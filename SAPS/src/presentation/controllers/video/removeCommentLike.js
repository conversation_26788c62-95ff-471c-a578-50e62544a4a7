import { removeCommentLikeService } from "../../../application/services/video/removeCommentLikeService.js";

export async function removeCommentLikeController(req, res) {
    try {
        const { commentId, userId } = req.body;

        const isDeleted = await removeCommentLikeService({ commentId, userId });
        res.status(200).send(isDeleted);
    } catch (error) {
        res.status(500).send("Server error");
    }
}