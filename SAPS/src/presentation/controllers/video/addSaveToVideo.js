import { customError } from '../../../application/customErrors/index.js';
import addSaveToVideoService from '../../../application/services/video/addSaveToVideoService.js';
export async function addSaveToVideoController(req, res) {
    try {
        console.log("Trigger add save to video controller");
        const { userId, videoId } = req.body;
        console.log(userId, videoId);
        const newLike = await addSaveToVideoService({ userId, videoId });
        res.status(200).send(newLike);
    } catch (error) {
        if (error instanceof customError) {
            return res.status(404).send({ message: error.message })
        }
        res.status(500).send("Server error");
    }
}