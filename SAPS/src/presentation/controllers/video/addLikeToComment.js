import { addLikeToCommentService } from "../../../application/services/video/addLikeToComment.js";
import { NotFoundError } from "../../../application/customErrors/index.js";

export async function addLikeToCommentController(req, res) {
    try {
        const newLike = await addLikeToCommentService({ ...req.body });
        res.status(200).send(newLike);
    } catch (error) {
        if (error instanceof NotFoundError) {
            return res.status(404).send({ message: error.message });
            
        }
        res.status(500).send("Server error");
    }
}