import { createVideoService } from "../../../application/services/video/createVideoService.js";

export async function createVideoController(req, res) {
    try {
        const { title, teacherId, videoUrl, subject, thumbnail,description,tags } = req.body;
        console.log(req.body)
        const newVideo = await createVideoService({ title, teacherId, videoUrl, subject, thumbnail,description,tags });
        res.status(200).send(newVideo);
    } catch (error) {
        res.status(500).send(error);
    }
}