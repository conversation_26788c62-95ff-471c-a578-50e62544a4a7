import { updatePlaylistService } from "../../../application/services/playlist/updatePlaylistService.js";

export async function updatePlaylistController(req, res) {
    try {

        const { title, subject, type } = req.body;
        const {playlistId } = req.params;
        const newPlaylist = await updatePlaylistService({playlistId, title, subject, type });
        res.status(200).send(newPlaylist);
    } catch (error) {
        res.status(500).send(error);
    }
}