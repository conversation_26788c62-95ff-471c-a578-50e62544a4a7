import addVideoToPlaylistService from "../../../application/services/playlist/addVideoToPlaylistService.js";

export async function addVideosToPlaylistController(req, res) {
    try {
        const  playlistId  = parseInt(req.params.playlistId);
        const {videosIds} = req.body;
        const newPlaylist = await addVideoToPlaylistService({playlistId, videosIds });
        res.status(200).send(newPlaylist);
    } catch (error) {
        res.status(500).send(error);
    }
}