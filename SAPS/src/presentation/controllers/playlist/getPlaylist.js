import getPlaylistService from "../../../application/services/playlist/getPlaylistService.js";

export default async function getPlaylistController(req,res) {
    try {
        const playlistId = parseInt(req.params.playlistId);
        const playlist = await getPlaylistService({playlistId});

        return res.status(200).send(playlist);
    } catch (error) {
        return res.status(500).send(`server error: ${error}`);
    }
} 