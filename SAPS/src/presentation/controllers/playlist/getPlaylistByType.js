import { getPlaylistByTypeService } from "../../../application/services/playlist/getPlaylistByType.js";

export async function getPlaylistByTypeController(req, res) {
    try {
        const { type } = req.params;
        const { subject } = req.body;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;

        const playlists = await getPlaylistByTypeService({ page, limit, type, subject });

        res.status(200).send(playlists);
    } catch (error) {
        res.status(500).send(`server error: ${error.message}`)
    }
}