import getPlaylistsByFilterService from "../../../application/services/playlist/getPlaylistsByFilter.js";

export async function getPlaylistsByFilterController(req, res) {
    try {
        console.log(req.query);
        let { filter } = req.query;

        // Check if filter is a string and parse it to an object
        if (typeof filter === 'string') {
            // Parse the filter string to an object
            filter = JSON.parse(filter);
        }

        // Iterate through the filter keys to check for string representations of arrays
        for (const key in filter) {
            if (typeof filter[key] === 'string') {
                try {
                    // Try to parse the string; if it's a valid JSON array representation, it will succeed
                    const parsedValue = JSON.parse(filter[key]);
                    // Check if the parsed value is an array
                    if (Array.isArray(parsedValue)) {
                        filter[key] = parsedValue; // Replace with the parsed array
                    }
                } catch (error) {
                    // Ignore errors from parsing if the string is not a valid JSON array
                }
            }
        }
        const playlists = await getPlaylistsByFilterService(filter);

        return res.status(200).send(playlists);
    } catch (error) {
        return res.status(500).send(`server error: ${error}`);
    }
}