import { getPlaylistByOwnerService } from "../../../application/services/playlist/getPlaylistByOwner.js";

export async function getPlaylistByOwnerController(req, res) {
    try {
        const { ownerId } = req.params;
        const { subject } = req.body;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;

        const playlists = await getPlaylistByOwnerService({ page, limit, ownerId, subject });

        res.status(200).send(playlists);
    } catch (error) {
        res.status(500).send(`server error: ${error.message}`)
    }
}