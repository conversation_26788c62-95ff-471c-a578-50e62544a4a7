import express from 'express';
import { addCommentToVideoController, addLikeToCommentController, addLikeToVideoController, addSaveToVideoController, addWatchedToVideoController, createVideoController, deleteVideoController, getLikedVideosController, getSavedVideosController, getVideosBySubjectController, getVideosController, getVideoWithStatsController, removeCommentLikeController } from '../controllers/index.js';
import { getVideoByIdController } from '../controllers/video/getVideoById.js';
const VideoRouter = express.Router();

VideoRouter.post('/', createVideoController);
VideoRouter.post('/like/', addLikeToVideoController);
VideoRouter.post('/save/', addSaveToVideoController);
VideoRouter.post('/watched/', addWatchedToVideoController);
VideoRouter.post('/comment/', addCommentToVideoController);
VideoRouter.post('/comment/like/', addLikeToCommentController);

VideoRouter.get('/', getVideosController);
VideoRouter.get('/likedBy/:userId', getLikedVideosController);
VideoRouter.get('/savedBy/:userId', getSavedVideosController);
// VideoRouter.get('/watchedBy/:userId', getWatchedVideosController); -- Não vamos usar esse endpoint por enquanto
VideoRouter.get('/subject/:subject', getVideosBySubjectController);
VideoRouter.get('/get/base/:id', getVideoByIdController);
VideoRouter.get('/get/withStats/:id', getVideoWithStatsController);


VideoRouter.delete('/:videoId', deleteVideoController);
VideoRouter.delete('/comment/like', removeCommentLikeController);
VideoRouter.delete('/comment/', removeCommentLikeController);
export default VideoRouter;