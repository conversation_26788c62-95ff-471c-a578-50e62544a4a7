// // tests/playlistRouter.test.js
// import request from 'supertest';
// import app from '../../../app.js';
// import { PrismaClient } from '@prisma/client';
// const prisma = new PrismaClient();

// describe('Playlist Router', () => {
//     let createdPlaylists = [];

//     beforeEach(async () => {
//         // Create test data
//         const teacher = await prisma.teacher.create({
//             data: {
//                 name: 'Test Teacher',
//                 description: 'A teacher for testing',
//             },
//         });

//         console.log({ teacher })

//         const playlist = await prisma.playlist.create({
//             data: {
//                 title: 'Test Playlist',
//                 subject: ['Math'],
//                 olympiads: ['1'],
//                 levelRange: [1, 5],
//                 type: 'LOCAL',
//                 ownerId: teacher.userId || 'atomize',
//                 PlaylistVideo: []
//             },
//         });

//         console.log({ playlist })

//         createdPlaylists.push(playlist.id);
//     });

//     afterEach(async () => {
//         // Delete only the test data
//         await Promise.all([
//             prisma.playlist.deleteMany({
//                 where: {
//                     id: {
//                         in: createdPlaylists,
//                     },
//                 },
//             }),
//             prisma.teacher.deleteMany({
//                 where: {
//                     name: 'Test Teacher',
//                 },
//             }),
//         ]);

//         createdPlaylists = []; // Reset for next tests
//     });

//     test('Should be able to get playlist by filter', () => {

//     })
// });