import express from 'express';
import { createPlaylistController, getPlaylistByOwnerController, getPlaylistByTypeController, getPlaylistsByFilterController, getSinglePlaylistByFilterController } from '../controllers/playlist/index.js';
import { updatePlaylistController } from '../controllers/playlist/updatePlaylist.js';
import { addVideosToPlaylistController } from '../controllers/playlist/addVideoToPlaylist.js';
import getPlaylistController from '../controllers/playlist/getPlaylist.js';
import deletePlaylistController from '../controllers/playlist/deletePlaylist.js';
const PlaylistRouter = express.Router();

PlaylistRouter.post('/', createPlaylistController);
PlaylistRouter.post('/update/:playlistId', updatePlaylistController);
PlaylistRouter.post('/add/videos/:playlistId', addVideosToPlaylistController);

PlaylistRouter.get('/get/:playlistId', getPlaylistController);
PlaylistRouter.get('/filter/single/', getSinglePlaylistByFilterController);
PlaylistRouter.get('/filter/', getPlaylistsByFilterController);
PlaylistRouter.get('/type/:type', getPlaylistByTypeController);
PlaylistRouter.get('/user/:ownerId', getPlaylistByOwnerController);


PlaylistRouter.delete('/:playlistId', deletePlaylistController);
export default PlaylistRouter;