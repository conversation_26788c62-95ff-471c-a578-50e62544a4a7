import VideoRepository from '../../../domain/repositories/videoRepository.js';

const videoRepository = new VideoRepository();

export async function removeCommentLikeService({ commentId, userId }) {
    try {
        const deletedCommentLike = await videoRepository.deleteCommentLike({ commentId, userId });
        return deletedCommentLike;
    } catch (error) {
        console.error(`Error deleting comment like: ${error.message}`);
        throw error;
    }
}