import CdsApi from '../../../infra/ATOMIZE/CDS/CdsApi.js';
import { NotFoundError } from '../../customErrors/index.js';

export async function getCommentsByGroupService({ 
  groupId, 
  videoId, 
  isDoubt, 
  page = 1, 
  limit = 20, 
  token 
}) {
  try {
    console.log("SAPS getCommentsByGroupService called with:", {
      groupId,
      videoId,
      isDoubt,
      page,
      limit
    });

    if (!groupId) {
      throw new Error("groupId is required");
    }

    const commentsData = await CdsApi.getCommentsByGroupId({
      groupId,
      videoId,
      isDoubt,
      page,
      limit,
      token
    });

    return commentsData;
  } catch (error) {
    console.error(`Error getting comments by group: ${error.message}`);
    throw error;
  }
}
