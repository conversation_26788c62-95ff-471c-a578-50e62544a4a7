import VideoRepository from '../../../domain/repositories/videoRepository.js';

const videoRepository = new VideoRepository();

export async function removeVideoSaveService({ videoId, userId }) {
    try {
        const deletedVideoSave = await videoRepository.deleteVideoSave({ videoId, userId });
        return deletedVideoSave;
    } catch (error) {
        console.error(`Error deleting comment save: ${error.message}`);
        throw error;
    }
}