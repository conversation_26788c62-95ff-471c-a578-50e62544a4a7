import VideoRepository from "../../../domain/repositories/videoRepository.js";

export async function getLikedVideosService({ page = 0, limit = 10, userId }) {
    const videoRepository = new VideoRepository();
    try {
        const likedVideos = await videoRepository.getLikedVideos(page, limit, userId);
        return likedVideos;
    } catch (error) {
        console.error(`Error retrieving liked videos: ${error.message}`);
        throw error;
    }
}