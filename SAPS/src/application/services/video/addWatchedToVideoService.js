import VideoRepository from '../../../domain/repositories/videoRepository.js';
import CdsApi from '../../../infra/ATOMIZE/CDS/CdsApi.js';
import MatrixisHandler from '../../../infra/ATOMIZE/Matrixis/messageHandler.js';
import { NotFoundError } from '../../customErrors/index.js';

// const videoRepository = new VideoRepository();

export default async function addWatchedToVideoService({ videoId, playlistId, userId, token }) {
    try {
        
        // dá para comentar, pois não vamos usar o BD relacional para isto   
        // const videoAggregateRoot = await videoRepository.findById(videoId);
        // const watchedData = await videoAggregateRoot.addWatched(userId); 
        console.log("INSIDE SAPS addVideoWatched", { userId, videoId, playlistId, token })
        const isVideoWatched = (await CdsApi.addVideoWatched({ 
            id: userId, 
            videoId,
            playlistId,
            token
        }).catch(err => console.log(err)));
        console.log("IS VIDEO WATCHED", isVideoWatched)
        console.log("IS VIDEO WATCHED.IS WATCHED", isVideoWatched.isWatched)
        if(isVideoWatched) {
            MatrixisHandler.publishVideoWatchedXp({ userId, videoId });
            return isVideoWatched.isWatched;
            // Comentado por enquanto para salvar apenas no banco de dados do CDS
            // if (isVideoWatched) {
            //     await videoRepository.addWatched(watchedData);
            // } else {
            //     await videoRepository.deleteVideoWatched(watchedData);
            // }
        }

        return false;
    } catch (error) {
        console.error(`Error adding watched to video: ${error.message}`);
        throw error;
    }
}