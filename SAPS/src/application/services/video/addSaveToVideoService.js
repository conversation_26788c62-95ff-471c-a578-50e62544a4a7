import VideoRepository from '../../../domain/repositories/videoRepository.js';
import CdsApi from '../../../infra/ATOMIZE/CDS/CdsApi.js';
import { NotFoundError } from '../../customErrors/index.js';

const videoRepository = new VideoRepository();

export default async function addSaveToVideoService({ videoId, userId, playlistId, token }) {
    try {
        const videoAggregateRoot = await videoRepository.findById(videoId);

        const saveData = await videoAggregateRoot.addSave(userId);
        console.log({ saveData })
        const isVideoSaved = (await CdsApi.addOrRemoveVideoSave({ id: userId, videoId, playlistId, token }).catch(err => console.log(err))).isSaved;

        if (isVideoSaved) { // TODO: remove these ?
            console.log("Saving video")
            await videoRepository.addSave(saveData);
        } else {
            console.log("UnSaving video")
            await videoRepository.deleteVideoSave(saveData);
        }
        return isVideoSaved;
    } catch (error) {
        console.error(`Error adding save to video: ${error.message}`);
        throw error;
    }
}