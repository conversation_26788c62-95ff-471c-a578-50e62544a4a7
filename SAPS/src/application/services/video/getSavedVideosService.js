import VideoRepository from "../../../domain/repositories/videoRepository.js";

export async function getSavedVideosService({ page = 0, limit = 10, userId }) {
    const videoRepository = new VideoRepository();

    try {
        const savedVideos = await videoRepository.getSavedVideos(page, limit, userId);
        return savedVideos;
    } catch (error) {
        console.error(`Error retrieving saved videos: ${error.message}`);
        throw error;
    }
}