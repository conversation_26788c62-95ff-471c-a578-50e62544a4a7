import VideoRepository from "../../../domain/repositories/videoRepository.js";
import CdsApi from "../../../infra/ATOMIZE/CDS/CdsApi.js";
import { NotFoundError } from '../../customErrors/index.js';

const videoRepository = new VideoRepository();
export default async function getVideoWithStatsService({ userId, videoId, playlistId , token}) {
    console.log({videoId, playlistId, userId, token})
    console.log("Calling findByIdWithMetrics with:", { videoId, userId })
    const videoWithStatistics = await videoRepository.findByIdWithMetrics({ videoId, userId });
    console.log("videoWithStatistics", videoWithStatistics)
    const videoWithStatistics_fromCDS = await CdsApi.getVideoWithStats({ id: userId, videoId, playlistId, token });
    const watched = videoWithStatistics_fromCDS?.watched || false;

    return { ...videoWithStatistics, userStatistics: { ...videoWithStatistics.userStatistics, hasUserWatched: watched } };
}