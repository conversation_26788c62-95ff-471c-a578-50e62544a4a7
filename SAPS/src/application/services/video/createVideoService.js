import VideoRepository from '../../../domain/repositories/videoRepository.js';
import VideoAggregateRoot from '../../../domain/aggregates/videoAggregateRoot.js';

const videoRepository = new VideoRepository();

export async function createVideoService({ title, teacherId, videoUrl, subject,thumbnail,description,tags }) {
    try {
        const videoAggregate = VideoAggregateRoot.fromData({ title, teacherId, videoUrl, subject,thumbnail,description,tags });
        const newVideo = await videoRepository.create(videoAggregate);
        return newVideo;
    } catch (error) {
        console.error(`Error creating video: ${error.message}`);
        throw error;
    }
}