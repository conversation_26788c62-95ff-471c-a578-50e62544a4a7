import CdsApi from '../../../infra/ATOMIZE/CDS/CdsApi.js';
import VideoRepository from '../../../domain/repositories/videoRepository.js';

const videoRepository = new VideoRepository();

/**
 * Batch service to get comments from multiple groups efficiently
 * This reduces API calls and improves performance for teachers with many groups
 */
export async function getCommentsByGroupsBatchService({
  groupIds,
  videoId,
  isDoubt = true,
  page = 1,
  limit = 20,
  token
}) {
  console.log('getCommentsByGroupsBatchService called with:', {
    groupCount: groupIds.length,
    videoId,
    isDoubt,
    page,
    limit
  });

  const allComments = [];
  const groupResults = [];
  const BATCH_SIZE = 10; // Process groups in batches to avoid overwhelming CDS

  try {
    // Process groups in batches to avoid overwhelming the CDS service
    const groupBatches = [];
    for (let i = 0; i < groupIds.length; i += BATCH_SIZE) {
      groupBatches.push(groupIds.slice(i, i + BATCH_SIZE));
    }

    console.log(`Processing ${groupIds.length} groups in ${groupBatches.length} batches of max ${BATCH_SIZE}`);

    for (const [batchIndex, batch] of groupBatches.entries()) {
      console.log(`Processing batch ${batchIndex + 1}/${groupBatches.length} with ${batch.length} groups`);
      
      const batchResults = await Promise.allSettled(
        batch.map(async (groupId) => {
          const startTime = Date.now();
          
          try {
            console.log(`Fetching comments for group ${groupId}`);
            
            // Call CDS to get comments for this group
            const cdsResponse = await CdsApi.getCommentsByGroupId({
              groupId,
              videoId,
              isDoubt,
              page: 1, // Always get first page for each group in batch
              limit: Math.ceil(limit / groupIds.length) + 5, // Distribute limit across groups with buffer
              token
            });

            const responseTime = Date.now() - startTime;
            console.log(`Group ${groupId} responded in ${responseTime}ms with ${cdsResponse.comments?.length || 0} comments`);

            if (cdsResponse.comments && cdsResponse.comments.length > 0) {
              // Enrich comments with video information from SAPS
              const enrichedComments = await enrichCommentsWithVideoInfo(cdsResponse.comments);
              
              return {
                groupId,
                success: true,
                comments: enrichedComments,
                commentCount: enrichedComments.length,
                responseTime
              };
            } else {
              return {
                groupId,
                success: true,
                comments: [],
                commentCount: 0,
                responseTime
              };
            }
          } catch (error) {
            const responseTime = Date.now() - startTime;
            console.error(`Error fetching comments for group ${groupId}:`, error.message);
            
            // Don't fail the entire batch for individual group errors
            return {
              groupId,
              success: false,
              error: error.message,
              comments: [],
              commentCount: 0,
              responseTime
            };
          }
        })
      );

      // Process batch results
      batchResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          const groupResult = result.value;
          groupResults.push({
            groupId: groupResult.groupId,
            success: groupResult.success,
            error: groupResult.error,
            commentCount: groupResult.commentCount
          });
          
          if (groupResult.success && groupResult.comments) {
            allComments.push(...groupResult.comments);
          }
        } else {
          console.error('Batch result rejected:', result.reason);
          groupResults.push({
            groupId: 'unknown',
            success: false,
            error: result.reason?.message || 'Unknown error',
            commentCount: 0
          });
        }
      });

      // Small delay between batches to be nice to CDS
      if (batchIndex < groupBatches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Sort all comments by creation date (newest first)
    const sortedComments = allComments.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    // Apply pagination to the final sorted result
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedComments = sortedComments.slice(startIndex, endIndex);

    const successfulGroups = groupResults.filter(g => g.success).length;
    const totalComments = sortedComments.length;

    console.log('getCommentsByGroupsBatchService completed:', {
      totalGroups: groupIds.length,
      successfulGroups,
      failedGroups: groupIds.length - successfulGroups,
      totalComments,
      paginatedComments: paginatedComments.length
    });

    return {
      comments: paginatedComments,
      groupResults,
      pagination: {
        page,
        limit,
        total: totalComments,
        totalPages: Math.ceil(totalComments / limit)
      },
      summary: {
        totalGroups: groupIds.length,
        successfulGroups,
        failedGroups: groupIds.length - successfulGroups,
        totalComments
      }
    };

  } catch (error) {
    console.error('Error in getCommentsByGroupsBatchService:', error);
    throw error;
  }
}

/**
 * Enrich CDS comment data with video information from SAPS
 */
async function enrichCommentsWithVideoInfo(comments) {
  if (!comments || comments.length === 0) return [];

  try {
    // Get unique video IDs
    const videoIds = [...new Set(comments.map(comment => comment.videoId))];
    
    console.log(`Enriching ${comments.length} comments with video info for ${videoIds.length} videos`);

    // Fetch video information for all unique video IDs
    const videoInfoPromises = videoIds.map(async (videoId) => {
      try {
        const video = await videoRepository.findById(videoId);
        return {
          id: videoId,
          title: video.title,
          description: video.description
        };
      } catch (error) {
        console.error(`Error fetching video info for ${videoId}:`, error.message);
        return {
          id: videoId,
          title: `Video ${videoId}`,
          description: ''
        };
      }
    });

    const videoInfoResults = await Promise.allSettled(videoInfoPromises);
    const videoInfoMap = new Map();

    videoInfoResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const videoInfo = result.value;
        videoInfoMap.set(videoInfo.id, videoInfo);
      } else {
        const videoId = videoIds[index];
        console.error(`Failed to get video info for ${videoId}:`, result.reason);
        videoInfoMap.set(videoId, {
          id: videoId,
          title: `Video ${videoId}`,
          description: ''
        });
      }
    });

    // Enrich comments with video information
    const enrichedComments = comments.map(comment => ({
      ...comment,
      video: videoInfoMap.get(comment.videoId),
      videoTitle: videoInfoMap.get(comment.videoId)?.title
    }));

    console.log(`Successfully enriched ${enrichedComments.length} comments`);
    return enrichedComments;

  } catch (error) {
    console.error('Error enriching comments with video info:', error);
    // Return original comments if enrichment fails
    return comments;
  }
}
