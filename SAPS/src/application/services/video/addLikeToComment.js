import VideoRepository from '../../../domain/repositories/videoRepository.js';
import CdsApi from '../../../infra/ATOMIZE/CDS/CdsApi.js';
const videoRepository = new VideoRepository();

export async function addLikeToCommentService({ commentId, userId, token }) {
    try {
        commentId = parseInt(commentId);
        const videoAggregateRoot = await videoRepository.findCommentById(commentId);

        const userLikeMetric = await CdsApi.addOrRemoveCommentLikeFromUser({ userId, commentId, token });
        const isCommentLiked = userLikeMetric.likedComments[commentId.toString()];
        let like
        if (isCommentLiked) {
            like= await videoRepository.addCommentLike({ userId, commentId });
        }
        else {
            like = await videoRepository.deleteCommentLike({ userId, commentId });
        }
        return {userLikeMetric,like};
    } catch (error) {
        console.error(`Error adding like to video: ${error.message}`);
        throw error;
    }
}