import VideoRepository from "../../../domain/repositories/videoRepository.js";

export async function getVideosBySubjectService({ page = 0, limit = 10, subject }) {
    const videoRepository = new VideoRepository();

    try {
        const videos = await videoRepository.getWithFilters({ page, limit, filter: { subject } });
        return videos;
    } catch (error) {
        console.error(`Error retrieving videos: ${error.message}`);
        throw error;
    }
}