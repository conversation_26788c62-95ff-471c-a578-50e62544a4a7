import { videoRepository } from '../../../domain/repositories/index.js';
import { NotFoundError, ValidationError } from '../../customErrors/index.js';
import CdsApi from '../../../infra/ATOMIZE/CDS/CdsApi.js';

/**
 * Service to handle teacher response to student doubts
 * This service:
 * 1. Validates the doubt exists and is pending (status -1)
 * 2. Updates doubt status to 0 (teacher responding) in CDS
 * 3. Creates a communication channel for teacher-student chat
 * 4. Returns channel information for frontend to open chat
 */
export async function respondToDoubtService({
  commentId,
  teacherId,
  teacherName,
  token
}) {
  console.log(`DEBUG - [RespondToDoubt] Starting response process: commentId=${commentId}, teacherId=${teacherId}`);
  
  try {
    // 1. Validate comment exists and get comment info
    const commentInfo = await videoRepository.getCommentInfo(commentId);
    if (!commentInfo) {
      throw new NotFoundError('Comment', `Comment with id ${commentId} not found`);
    }

    if (!commentInfo.isDoubt) {
      throw new ValidationError('Comment is not a doubt');
    }

    console.log(`DEBUG - [RespondToDoubt] Found doubt: ${commentInfo.content.substring(0, 50)}...`);

    // 2. Update doubt status to 0 (teacher responding) in CDS
    try {
      await CdsApi.updateDoubtStatus({
        commentId: commentId.toString(),
        status: 0, // Teacher responding
        token
      });
      console.log(`DEBUG - [RespondToDoubt] Updated doubt status to 0 in CDS`);
    } catch (error) {
      console.error(`ERROR - [RespondToDoubt] Failed to update doubt status in CDS:`, error);
      throw new Error('Failed to update doubt status');
    }

    // 3. Create communication channel
    const channelData = {
      name: `Dúvida: ${commentInfo.video?.title || 'Vídeo'} - ${commentInfo.username}`,
      members: [teacherId, commentInfo.userId],
      type: 'CHAT',
      contextType: 'DOUBT',
      contextId: commentId.toString()
    };

    console.log(`DEBUG - [RespondToDoubt] Creating communication channel:`, channelData);

    // Call communication service to create channel
    const communicationResponse = await fetch(`${process.env.CDS_API_URL}/communication/channels`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token
      },
      body: JSON.stringify(channelData)
    });

    if (!communicationResponse.ok) {
      const errorData = await communicationResponse.text();
      console.error(`ERROR - [RespondToDoubt] Communication API failed:`, {
        status: communicationResponse.status,
        statusText: communicationResponse.statusText,
        error: errorData
      });
      throw new Error(`Failed to create communication channel: ${communicationResponse.status}`);
    }

    const channelResult = await communicationResponse.json();
    console.log(`DEBUG - [RespondToDoubt] Successfully created channel:`, channelResult.data?.id);

    // 4. Return success response with channel info
    return {
      success: true,
      message: 'Teacher response initiated successfully',
      data: {
        commentId,
        channelId: channelResult.data?.id,
        channelName: channelData.name,
        studentId: commentInfo.userId,
        studentName: commentInfo.username,
        videoTitle: commentInfo.video?.title,
        doubtContent: commentInfo.content
      }
    };

  } catch (error) {
    console.error(`ERROR - [RespondToDoubt] Service failed:`, {
      commentId,
      teacherId,
      error: error.message,
      stack: error.stack
    });
    
    // Re-throw known errors
    if (error instanceof NotFoundError || error instanceof ValidationError) {
      throw error;
    }
    
    // Wrap unknown errors
    throw new Error(`Failed to respond to doubt: ${error.message}`);
  }
}
