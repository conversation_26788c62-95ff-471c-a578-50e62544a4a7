import { videoRepository } from '../../../domain/repositories/index.js';
import { NotFoundError } from '../../customErrors/index.js';
import CdsApi from '../../../infra/ATOMIZE/CDS/CdsApi.js';

/**
 * Optimized comment creation service with improved performance
 * Features:
 * - Async CDS synchronization for better user experience
 * - Playlist ID caching to reduce database queries
 * - Performance monitoring integration
 * - Graceful error handling with fallback mechanisms
 */

// Simple in-memory cache for playlist IDs (valid for 10 minutes)
const playlistCache = new Map();
const PLAYLIST_CACHE_TTL = 10 * 60 * 1000; // 10 minutes

/**
 * Get playlist ID with caching
 */
async function getCachedPlaylistId(videoId) {
    const cacheKey = `playlist_${videoId}`;
    const cached = playlistCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < PLAYLIST_CACHE_TTL) {
        console.log(`DEBUG - Using cached playlistId for video ${videoId}: ${cached.playlistId}`);
        return cached.playlistId;
    }
    
    // Fetch from database
    const playlistId = await videoRepository.getPlaylistIdByVideoId(videoId);
    
    // Cache the result
    playlistCache.set(cacheKey, {
        playlistId,
        timestamp: Date.now()
    });
    
    console.log(`DEBUG - Cached playlistId for video ${videoId}: ${playlistId}`);
    return playlistId;
}

/**
 * Async CDS synchronization with retry logic
 */
async function syncWithCdsAsync(commentData) {
    const { userId, videoId, commentId, isDoubt, token } = commentData;
    const maxRetries = isDoubt ? 3 : 2; // More retries for doubts
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            // Get playlist ID (with caching)
            const playlistId = await getCachedPlaylistId(videoId);
            
            console.log(`DEBUG - [Async] Syncing comment with CDS: commentId=${commentId}, isDoubt=${isDoubt} (attempt ${attempt}/${maxRetries})`);
            
            await CdsApi.addOrRemoveCommentFromUser({
                userId,
                videoId,
                commentId,
                isDoubt,
                isRemove: false,
                playlistId,
                token
            });
            
            console.log(`DEBUG - [Async] Successfully synced comment ${commentId} with CDS on attempt ${attempt}`);
            return { success: true, attempt };
            
        } catch (error) {
            console.error(`ERROR - [Async] CDS sync attempt ${attempt}/${maxRetries} failed:`, {
                error: error.message,
                status: error.response?.status,
                commentId,
                userId,
                videoId
            });
            
            if (attempt < maxRetries) {
                const delay = Math.min(attempt * 1000, 5000); // Cap at 5 seconds
                console.log(`DEBUG - [Async] Retrying CDS sync in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    
    // All retries failed
    console.error(`CRITICAL - [Async] Failed to sync comment ${commentId} with CDS after ${maxRetries} attempts`);
    
    // For doubts, this is critical as they won't appear in teacher panel
    if (isDoubt) {
        console.error(`DOUBT SYNC FAILURE - Comment ${commentId} will NOT appear in teacher panel!`);
        // TODO: Add to dead letter queue for manual processing
    }
    
    return { success: false, maxRetries };
}

/**
 * Main optimized comment creation service
 */
export async function addCommentToVideoServiceOptimized({ 
    videoId, 
    content, 
    isDoubt, 
    parentCommentId = null, 
    userId, 
    username, 
    token 
}) {
    const startTime = Date.now();
    
    try {
        console.log(`DEBUG - [Optimized] Creating comment: videoId=${videoId}, isDoubt=${isDoubt}, userId=${userId}`);
        
        // 1. Find video and create comment (core operation)
        const video = await videoRepository.findById(videoId);
        let newCommentData = null;

        if (parentCommentId) {
            const comments = await video.getComments();
            const parentComment = comments.find(comment => comment.id === parentCommentId);
            if (!parentComment) {
                throw new NotFoundError("Comment", `Comment with id ${parentCommentId} not found`);
            }
            newCommentData = await video.addCommentToComment({
                commentId: parentCommentId,
                content,
                userId,
                username
            });
        } else {
            newCommentData = await video.addComment({ content, isDoubt, userId, username });
        }

        // 2. Save comment to database
        const savedComment = await videoRepository.addComment(newCommentData);
        const commentId = savedComment.id;
        
        console.log(`DEBUG - [Optimized] Comment created successfully: commentId=${commentId}`);
        
        // 3. Prepare response data immediately (don't wait for CDS sync)
        const responseData = {
            id: commentId,
            content: savedComment.content,
            isDoubt: savedComment.isDoubt,
            userId: savedComment.userId,
            username: savedComment.username,
            videoId: savedComment.videoId,
            parentCommentId: savedComment.parentCommentId,
            createdAt: savedComment.createdAt
        };
        
        // 4. Start async CDS synchronization (non-blocking)
        if (token) {
            const syncPromise = syncWithCdsAsync({
                userId,
                videoId,
                commentId,
                isDoubt,
                token
            });
            
            // Don't await - let it run in background
            syncPromise.catch(error => {
                console.error(`BACKGROUND - CDS sync failed for comment ${commentId}:`, error);
            });
            
            console.log(`DEBUG - [Optimized] Started background CDS sync for comment ${commentId}`);
        } else {
            console.warn(`WARNING - No token provided, skipping CDS sync for comment ${commentId}`);
        }
        
        // 5. Return response immediately (improved user experience)
        const totalTime = Date.now() - startTime;
        console.log(`DEBUG - [Optimized] Comment creation completed in ${totalTime}ms (excluding CDS sync)`);
        
        return responseData;
        
    } catch (error) {
        const totalTime = Date.now() - startTime;
        console.error(`ERROR - [Optimized] Comment creation failed after ${totalTime}ms:`, {
            error: error.message,
            videoId,
            userId,
            isDoubt
        });
        throw error;
    }
}

/**
 * Synchronous version for critical operations (doubts)
 * Use this when you need to ensure CDS sync completes before responding
 */
export async function addCommentToVideoServiceSync({ 
    videoId, 
    content, 
    isDoubt, 
    parentCommentId = null, 
    userId, 
    username, 
    token 
}) {
    const startTime = Date.now();
    
    try {
        console.log(`DEBUG - [Sync] Creating comment: videoId=${videoId}, isDoubt=${isDoubt}, userId=${userId}`);
        
        // 1. Create comment (same as optimized version)
        const video = await videoRepository.findById(videoId);
        let newCommentData = null;

        if (parentCommentId) {
            const comments = await video.getComments();
            const parentComment = comments.find(comment => comment.id === parentCommentId);
            if (!parentComment) {
                throw new NotFoundError("Comment", `Comment with id ${parentCommentId} not found`);
            }
            newCommentData = await video.addCommentToComment({
                commentId: parentCommentId,
                content,
                userId,
                username
            });
        } else {
            newCommentData = await video.addComment({ content, isDoubt, userId, username });
        }

        const savedComment = await videoRepository.addComment(newCommentData);
        const commentId = savedComment.id;
        
        // 2. Synchronous CDS sync (wait for completion)
        if (token) {
            const syncResult = await syncWithCdsAsync({
                userId,
                videoId,
                commentId,
                isDoubt,
                token
            });
            
            if (!syncResult.success && isDoubt) {
                // For doubts, CDS sync failure is critical
                throw new Error(`Failed to sync doubt with CDS after ${syncResult.maxRetries} attempts`);
            }
        }
        
        const totalTime = Date.now() - startTime;
        console.log(`DEBUG - [Sync] Comment creation completed in ${totalTime}ms (including CDS sync)`);
        
        return {
            id: commentId,
            content: savedComment.content,
            isDoubt: savedComment.isDoubt,
            userId: savedComment.userId,
            username: savedComment.username,
            videoId: savedComment.videoId,
            parentCommentId: savedComment.parentCommentId,
            createdAt: savedComment.createdAt
        };
        
    } catch (error) {
        const totalTime = Date.now() - startTime;
        console.error(`ERROR - [Sync] Comment creation failed after ${totalTime}ms:`, error.message);
        throw error;
    }
}

/**
 * Clear playlist cache (useful for testing or cache invalidation)
 */
export function clearPlaylistCache() {
    playlistCache.clear();
    console.log('DEBUG - Playlist cache cleared');
}

/**
 * Get cache statistics
 */
export function getPlaylistCacheStats() {
    return {
        size: playlistCache.size,
        entries: Array.from(playlistCache.entries()).map(([key, value]) => ({
            key,
            playlistId: value.playlistId,
            age: Date.now() - value.timestamp
        }))
    };
}

// Backward compatibility: export optimized version as default
export { addCommentToVideoServiceOptimized as addCommentToVideoService };
