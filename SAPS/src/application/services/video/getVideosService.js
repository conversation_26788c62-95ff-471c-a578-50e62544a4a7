import VideoRepository from "../../../domain/repositories/videoRepository.js";

export async function getVideosService(page = 0, limit = 10) {
    const videoRepository = new VideoRepository();

    try {
        const videos = await videoRepository.getPaginatedVideos(page, limit);
        return videos;
    } catch (error) {
        console.error(`Error retrieving paginated videos: ${error.message}`);
        throw error;
    }
}