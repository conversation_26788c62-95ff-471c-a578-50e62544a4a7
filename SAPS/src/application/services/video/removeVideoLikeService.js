import VideoRepository from '../../../domain/repositories/videoRepository.js';

const videoRepository = new VideoRepository();

export async function removeVideoLikeService({ videoId, userId }) {
    try {
        const deletedVideoLike = await videoRepository.deleteVideoLike({ videoId, userId });
        return deletedVideoLike;
    } catch (error) {
        console.error(`Error deleting comment like: ${error.message}`);
        throw error;
    }
}