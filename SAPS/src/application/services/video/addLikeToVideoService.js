import VideoRepository from '../../../domain/repositories/videoRepository.js';
import CdsApi from '../../../infra/ATOMIZE/CDS/CdsApi.js';

const videoRepository = new VideoRepository();

export async function addLikeToVideoService({ videoId, userId, playlistId, token }) {
    try {
        const videoAggregateRoot = await videoRepository.findById(videoId);

        const likeData = await videoAggregateRoot.addLike(userId);
        console.log({ likeData })
        const changedFieldsObject = await CdsApi.addOrRemoveVideoLike({ id: userId, videoId, playlistId, token });
        const isVideoLiked = changedFieldsObject.isLiked;

        if (isVideoLiked) { // TODO: remove these ?
            console.log("Liking video")
            await videoRepository.addLike(likeData);
        } else {
            console.log("Unliking video")
            await videoRepository.deleteVideoLike(likeData);
        }

        return isVideoLiked;
    } catch (error) {
        console.error(`Error adding like to video: ${error.message}`);
        throw error;
    }
}