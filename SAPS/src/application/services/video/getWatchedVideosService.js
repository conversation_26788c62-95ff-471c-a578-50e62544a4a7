// Como não vamos usar o BD relacional para isto, não precisamos desse service por enquanto

// import VideoRepository from "../../../domain/repositories/videoRepository.js";

// export async function getWatchedVideosService({ page = 0, limit = 10, userId }) {
//     const videoRepository = new VideoRepository();

//     try {
//         const watchedVideos = await videoRepository.getWatchedVideos(page, limit, userId);
//         return watchedVideos;
//     } catch (error) {
//         console.error(`Error retrieving watched videos: ${error.message}`);
//         throw error;
//     }
// }