import TeacherRepository from "../../../domain/repositories/teacherRepository.js";
import TeacherAggregateRoot from "../../../domain/aggregates/teacherAggregateRoot.js";

const teacherRepository = new TeacherRepository();

export async function createTeacherService({ name, description, awards = [] }) {
    try {
        const teacherData = TeacherAggregateRoot.fromData({ name, description, awards });
        const newTeacher = await teacherRepository.create(teacherData);
        return newTeacher;
    } catch (error) {
        console.error(`Error creating teacher: ${error.message}`);
        throw error;
    }
}