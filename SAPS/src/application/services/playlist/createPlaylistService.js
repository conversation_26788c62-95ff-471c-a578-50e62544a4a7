import PlaylistAggregateRoot from "../../../domain/aggregates/playlistAggregateRoot.js";
import PlaylistRepository from "../../../domain/repositories/playlistRepository.js";

const playlistRepository = new PlaylistRepository();

export async function createPlaylistService({ title, subject, videos, type, ownerId,olympiads,levelRange = [] }) {
    try {
        const playlistData = PlaylistAggregateRoot.fromData({ title, videos, type, ownerId, subject,olympiads,levelRange });
        const newPlaylist = await playlistRepository.create(playlistData);
        return newPlaylist;
    } catch (error) {
        console.error(`Error creating teacher: ${error.message}`);
        throw error;
    }
}