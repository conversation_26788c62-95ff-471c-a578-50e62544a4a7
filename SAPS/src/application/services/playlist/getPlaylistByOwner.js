import PlaylistRepository from '../../../domain/repositories/playlistRepository.js'

export async function getPlaylistByOwnerService({ page = 0, limit = 10, ownerId, subject }) {
    const playlistRepository = new PlaylistRepository();

    try {
        let playlists = [];
        if (!!subject)
            playlists = await playlistRepository.getByOwner({ page, limit, ownerId, subject });
        else
            playlists = await playlistRepository.getByOwner({ page, limit, ownerId });
        return playlists;
    } catch (error) {
        console.error(`Error retrieving owner playlists: ${error.message}`);
        throw error;
    }
}