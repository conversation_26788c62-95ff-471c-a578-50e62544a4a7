import PlaylistRepository from "../../../domain/repositories/playlistRepository.js";

const playlistRepository = new PlaylistRepository();
export default async function getPlaylistService({playlistId}) {
    try {
        const playlist = await playlistRepository.findById(playlistId);
        return playlist;
    } catch (error) {
        console.error(`Error getting playlist: ${error.message}`);
        throw error;
    }
    
}