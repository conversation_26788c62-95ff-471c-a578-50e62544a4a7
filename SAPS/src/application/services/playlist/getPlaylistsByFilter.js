import PlaylistAggregateRoot from "../../../domain/aggregates/playlistAggregateRoot.js";
import PlaylistRepository from "../../../domain/repositories/playlistRepository.js";

export default async function getPlaylistsByFilterService(filter) {
    const { queryFilters, serverFilters } = PlaylistAggregateRoot.validateFilter(filter);
    const playlists = await PlaylistRepository.findByFilter({ queryFilters });
    if (serverFilters.levelRange) // TODO: bad extensibility
        playlists.filter(pl => pl.levelRange[0] >= levelRange[0] && pl.levelRange[1] <= levelRange[1])

    return playlists
}