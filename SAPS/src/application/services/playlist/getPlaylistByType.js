import PlaylistRepository from '../../../domain/repositories/playlistRepository.js'

export async function getPlaylistByTypeService({ page = 0, limit = 10, ownerId, type, subject }) {
    const playlistRepository = new PlaylistRepository();

    try {
        let playlists = [];
        if (!!subject)
            playlists = await playlistRepository.getByType({ page, limit, type, subject });
        else
            playlists = await playlistRepository.getByType({ page, limit, type, });
        return playlists;
    } catch (error) {
        console.error(`Error retrieving playlists: ${error.message}`);
        throw error;
    }
}