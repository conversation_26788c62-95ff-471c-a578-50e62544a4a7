import PlaylistAggregateRoot from "../../../domain/aggregates/playlistAggregateRoot.js";
import PlaylistRepository from "../../../domain/repositories/playlistRepository.js";

const playlistRepository = new PlaylistRepository();

export async function updatePlaylistService({playlistId, title, subject, type }) {
    try {
        const playlistAggregate = await playlistRepository.findById(playlistId);
        const playlistData = PlaylistAggregateRoot.fromData({playlistId, title, type, subject });
        const newPlaylist = await playlistRepository.update(playlistData);
        return newPlaylist;
    } catch (error) {
        console.error(`Error creating teacher: ${error.message}`);
        throw error;
    }
}