import PlaylistAggregateRoot from "../../../domain/aggregates/playlistAggregateRoot.js";
import PlaylistRepository from "../../../domain/repositories/playlistRepository.js";

export default async function getSinglePlaylistByFilterService(filter) {
    const { queryFilters } = PlaylistAggregateRoot.validateFilter(filter);
    const playlist = await PlaylistRepository.findSingleByFilter({ queryFilters });

    return playlist
}