import PlaylistRepository from "../../../domain/repositories/playlistRepository.js";

const playlistRepository = new PlaylistRepository();
export default async function addVideoToPlaylistService({ playlistId, videosIds }) {
    try {
        const playlistAggregate = await playlistRepository.findById(parseInt(playlistId));
        const playlistVideo = await playlistRepository.updateVideos(playlistAggregate.updateVideos({videosIds}))
        return playlistVideo;
    } catch (error) {
        console.error(`Error adding video to playlist: ${error.message}`);
        throw error;
    }
}