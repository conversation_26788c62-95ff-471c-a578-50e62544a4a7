import PlaylistRepository from "../../../domain/repositories/playlistRepository.js";

const playlistRepository = new PlaylistRepository();
export default async function deletePlaylistService({ playlistId }) {
    try {
        const playlistAggregate = await playlistRepository.findById(playlistId);
        const playlist = await playlistRepository.delete(playlistAggregate.id);
        return playlist;
    } catch (error) {
        console.error(`Error deleting playlist: ${error.message}`);
        throw error;
    }
}