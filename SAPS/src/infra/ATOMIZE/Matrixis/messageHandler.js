import { sendMessageToMatrixisQueue } from '../../../utils/aws/sqs.js';

class MatrixisHandler {
    static createMessageParams(eventType, data) {
        return {
            Message: JSON.stringify({
                eventType,
                data
            }),
        };
    }

    static async publishVideoWatchedXp({ userId, videoId }) {
        // Create message payload
        const messagePayload = {
            eventType: 'Video_Watched',
            data: { userId, videoId }
        };

        try {
            await sendMessageToMatrixisQueue("message-handler-queue", messagePayload);
            console.log(`Message sent to Matrixis queue successfully`);
        } catch (sqsError) {
            console.error('Error publishing message to SQS:', sqsError);
        }
    }
}

export default MatrixisHandler;