# Development files
.git/**
.gitignore
README.md
DOCS.md
*.md

# Test files
**/*.test.js
**/*.spec.js
**/__tests__/**
**/test/**
**/tests/**
coverage/**
.nyc_output/**

# Documentation
documentation/**

# Scripts
@scripts/**

# Development dependencies
node_modules/.cache/**
node_modules/.prisma/migrations/**
node_modules/.prisma/schema.prisma
node_modules/.prisma/libquery_engine-*
node_modules/.prisma/query-engine-*

# IDE files
.vscode/**
.idea/**
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/**

# Build artifacts
dist/**
build/**
.serverless/**

# Environment files
.env*
!.env.example

# Docker files
Dockerfile*
docker-compose*

# CI/CD
.github/**
.gitlab-ci.yml
.travis.yml

# Temporary files
tmp/**
temp/** 