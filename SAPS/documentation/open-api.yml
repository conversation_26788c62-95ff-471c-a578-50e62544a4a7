openapi: 3.0.0
info:
  title: Video API
  version: 1.0.0
  description: API specification for video management

paths:
  # Use `$ref` to reference paths defined in separate files
  /video:
    $ref: './paths/video.yml'
  /teacher:
    $ref: './paths/teacher.yml'
  /playlist:
    $ref: './paths/playlist.yml'

components:
  schemas:
    $ref: './components/schemas.yml'
  responses:
    $ref: './components/responses.yml'