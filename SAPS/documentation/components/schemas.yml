# This file contains the schemas for request and response bodies used across endpoints

CreateVideoRequest:
  type: object
  properties:
    title:
      type: string
      description: Title of the video
      example: "Introduction to Algebra"
    teacherId:
      type: string
      description: ID of the teacher uploading the video
      example: "teacher123"
    videoUrl:
      type: string
      description: URL of the video resource
      example: "http://example.com/video.mp4"
    subject:
      type: string
      description: Subject category for the video
      example: "Mathematics"
    thumbnail:
      type: string
      description: URL of the video thumbnail image
      example: "http://example.com/thumbnail.jpg"
    description:
      type: string
      description: A description of the video content
      example: "This video covers the basics of algebra."
    tags:
      type: array
      items:
        type: string
      description: Tags related to the video content
      example: ["math", "algebra", "education"]

Video:
  type: object
  properties:
    id:
      type: string
      description: Unique identifier for the video
      example: "video123"
    title:
      type: string
      description: Title of the video
      example: "Introduction to Algebra"
    teacherId:
      type: string
      description: ID of the teacher who uploaded the video
      example: "teacher123"
    videoUrl:
      type: string
      description: URL of the video resource
      example: "http://example.com/video.mp4"
    subject:
      type: string
      description: Subject category for the video
      example: "Mathematics"
    thumbnail:
      type: string
      description: URL of the video thumbnail image
      example: "http://example.com/thumbnail.jpg"
    description:
      type: string
      description: A description of the video content
      example: "This video covers the basics of algebra."
    tags:
      type: array
      items:
        type: string
      description: Tags related to the video content
      example: ["math", "algebra", "education"]
    likes:
      type: integer
      description: Number of likes on the video
      example: 124
    comments:
      type: array
      description: List of comments on the video
      items:
        $ref: '#/components/schemas/Comment'
    createdAt:
      type: string
      format: date-time
      description: Date when the video was created
      example: "2023-10-25T13:45:00Z"

Comment:
  type: object
  properties:
    id:
      type: string
      description: Unique identifier for the comment
      example: "comment789"
    userId:
      type: string
      description: ID of the user who made the comment
      example: "user456"
    content:
      type: string
      description: Content of the comment
      example: "Great video!"
    isDoubt:
      type: boolean
      description: Indicates if the comment is a doubt
      example: false
    likes:
      type: integer
      description: Number of likes on the comment
      example: 23
    createdAt:
      type: string
      format: date-time
      description: Date when the comment was made
      example: "2023-10-25T14:00:00Z"
