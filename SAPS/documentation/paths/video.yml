post:
  summary: Create a new video
  operationId: createVideo
  requestBody:
    description: Data required to create a new video
    required: true
    content:
      application/json:
        schema:
          $ref: '../components/schemas.yml#/CreateVideoRequest'
  responses:
    '200':
      description: Video created successfully
      content:
        application/json:
          schema:
            $ref: '../components/schemas.yml#/Video'
    '500':
      description: Server error

'/video/comment/':
  post:
    summary: Add a comment to a video
    operationId: addCommentToVideo
    requestBody:
      description: Data for adding a comment
      required: true
      content:
        application/json:
          schema:
            $ref: '../components/schemas.yml#/AddCommentRequest'
    responses:
      '200':
        description: Comment added successfully
        content:
          application/json:
            schema:
              $ref: '../components/schemas.yml#/Comment'
      '404':
        description: Video not found
      '500':
        description: Server error

'/video/like/':
  post:
    summary: Like a video
    operationId: likeVideo
    requestBody:
      description: Data required to like a video
      required: true
      content:
        application/json:
          schema:
            $ref: '../components/schemas.yml#/LikeRequest'
    responses:
      '200':
        description: Video liked successfully
      '404':
        description: Video not found
      '500':
        description: Server error

'/video/comment/like/':
  post:
    summary: Like a comment
    operationId: likeComment
    requestBody:
      description: Data required to like a comment
      required: true
      content:
        application/json:
          schema:
            $ref: '../components/schemas.yml#/LikeRequest'
    responses:
      '200':
        description: Comment liked successfully
      '404':
        description: Comment not found
      '500':
        description: Server error

'/video/save/':
  post:
    summary: Save a video
    operationId: saveVideo
    requestBody:
      description: Data required to save a video
      required: true
      content:
        application/json:
          schema:
            $ref: '../components/schemas.yml#/SaveRequest'
    responses:
      '200':
        description: Video saved successfully
      '404':
        description: Video not found
      '500':
        description: Server error

'/video/get/{id}':
  get:
    summary: Get a video by ID
    operationId: getVideoById
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
    responses:
      '200':
        description: Video retrieved successfully
        content:
          application/json:
            schema:
              $ref: '../components/schemas.yml#/Video'
      '404':
        description: Video not found
      '500':
        description: Server error