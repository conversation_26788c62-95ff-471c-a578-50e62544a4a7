{"name": "saps", "version": "1.0.0", "description": "Stream And Play Service for Atomize vídeo related services", "main": "index.js", "scripts": {"test-integration": "jest --testMatch '**/*.integration.test.js'", "startdev": "nodemon --exec babel-node ./src/app.js", "build": "npm run lint && npm test", "lint": "eslint .", "test": "jest --passWithNoTests", "deploy:safe": "./scripts/safe-deploy.sh dev", "prisma:generate": "npx prisma generate --schema src/domain/models/prisma/schema.prisma", "prisma:migrate": "npx prisma migrate dev --schema=./src/domain/models/prisma/schema.prisma", "prisma:studio": "npx prisma studio --schema=./src/domain/models/prisma/schema.prisma", "prisma:deploy": "npx prisma migrate deploy --schema=./src/domain/models/prisma/schema.prisma", "prisma:clean": "if exist src\\domain\\models\\generated rmdir /s /q src\\domain\\models\\generated", "generate:playlists": "node ./@scripts/generatePlaylists.js", "deploy:optimized": "node deploy-optimized.js"}, "resolutions": {"path-to-regexp": "8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/Atomize-dev/SAPS.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Atomize-dev/SAPS/issues"}, "homepage": "https://github.com/Atomize-dev/SAPS#readme", "dependencies": {"@aws-sdk/client-sqs": "^3.749.0", "@prisma/client": "^5.17.0", "axios": "^1.7.4", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"aws-sdk": "^2.1659.0", "babel-cli": "^6.26.0", "babel-node": "^0.0.1-security", "nodemon": "^3.1.4", "prisma": "^5.17.0", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "babel-jest": "^29.7.0", "eslint": "^9.13.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.8.3", "supertest": "^7.0.0", "jest": "^29.7.0", "babel-loader": "^9.2.1", "serverless-domain-manager": "^8.0.0", "serverless-export-env": "^2.2.0", "serverless-iam-roles-per-function": "^3.2.0", "serverless-plugin-split-stacks": "^1.14.0", "serverless-plugin-warmup": "^8.3.0", "serverless-prune-plugin": "^2.1.0", "serverless-step-functions": "^3.21.2", "serverless-webpack": "^5.15.0", "serverless": "^3.38.0", "webpack": "^5.97.1", "webpack-node-externals": "^3.0.0", "copy-webpack-plugin": "^12.0.2", "csv-parser": "^3.0.0"}}