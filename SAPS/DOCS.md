# SAPS

## Resumo

O **SAPS** é uma API RESTful projetada para gerenciar dados de vídeos, playlists e transmissões ao vivo da plataforma.

## Sumário

- [SAPS](#saps)
  - [Resumo](#resumo)
  - [Sum<PERSON><PERSON>](#sumário)
  - [Informações](#informações)
  - [Como contribuir](#como-contribuir)
    - [Como adicionar um novo use case](#como-adicionar-um-novo-use-case)
      - [Adicionando um novo service:](#adicionando-um-novo-service)
      - [Adicionando um novo controller:](#adicionando-um-novo-controller)
        - [Estrutura base de código:](#estrutura-base-de-código)
        - [Código base do controller:](#código-base-do-controller)
  - [Autenticação](#autenticação)

## Informações
- Código do serviço: SAPS
- API_KEY:

## Como contribuir
### Como adicionar um novo use case
#### Adicionando um novo service:
  Para desenvolver um novo service va para a pasta [`src/application/services`](https://github.com/Atomize-dev/SAPS/tree/main/src/application/services).
  Adicione um novo arquivo na raiz do diretorio ou em uma sub-pasta o padrão de nomenclatura é : useCase**Service**.js
  A estrutura base de código é:

  ```ts
  import someAggregate from ""
  import someRepository from ""
  export default async function useCaseService({prop1,prop2}){
    const propAfterApplyingDomainRUles = someAggregate.applySomeDomainRule({prop1,prop2});
    const dbResponse = await someRepository.interactWithDB(propAfterApplyingDomainRUles);
    // further interact with dbResponse ...
    const something = MANYCODEINTERACTIONS();
    return something
  }
  ```
> **Observação**: A priori, não será necessário pegar o erro nos services(mas eventualmente eles podem ser tratados lá), já que estes sempre serão consumidos por controllers que obrigatoriamente possuem uma etapa de pegar erros.

#### Adicionando um novo controller:

Para adicionar um novo controller, vá para o diretório [`src/presentation/controllers`](https://github.com/Atomize-dev/SAPS/tree/main/src/presentation/controllers). Adicione um novo arquivo na raiz do diretório ou em uma subpasta. O padrão de nomenclatura é `useCase.js`.

##### Estrutura base de código:
- O papel do controller é:
  1. Receber a entrada da requisição HTTP e aplicar uma formatação básica, se necessário.
  2. Chamar métodos de serviço para lidar com a lógica de negócios, deixando as decisões específicas para o domínio serem tratadas pelo serviço.
  3. Opcionalmente, aplicar utilitários de infraestrutura, como gerenciar uploads de arquivos ou gerar URLs com um `fileHandler`. Essas ações não representam regras de negócio, mas sim funcionalidades de suporte.
- Apenas formatações básicas devem ser aplicadas no controller, como conversão de tipos simples (por exemplo, `string` para `number`) ou um `JSON.parse()` em strings. Quaisquer outras necessidades de parsing ou validação específicas do negócio são de responsabilidade das classes de domínio ou dos services no caso de formatações para uso em classes de domínio mas que não refletem regras de negócio em sí.
- Não aplique nenhuma lógica de domínio no controller.

##### Código base do controller:

```ts
import useCaseService from "";
import customError from "";

export async function useCaseController(req, res) {
  try {
    const { prop1, prop2 } = req.body; // req.params or req.query

    const parsedProp1 = parseInt(prop1); // parsing data
    const response = await useCaseService({ prop1: parsedProp1, prop2 });

    res.status(200).send(response);
  } catch (error) {
    if (error instanceof customError) {
      res.status(error.code).send(error.message);
    } else {
      res.status(500).send(error);
    }
  }
}
```

## Autenticação

Para utilizar as rotas da API, é necessário autenticar-se com um token JWT. O token deve ser incluído no cabeçalho `Authorization` em todas as requisições: