CreateVideo:
  handler: src/presentation/handlers/video/createVideo.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-CreateVideo-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - s3:PutObject
        - s3:GetObject
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video
        method: post
        cors: true
        authorizer: authorizer

AddLikeToVideo:
  handler: src/presentation/handlers/video/addLikeToVideo.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddLikeToVideo-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/like
        method: post
        cors: true
        authorizer: authorizer

AddSaveToVideo:
  handler: src/presentation/handlers/video/addSaveToVideo.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddSaveToVideo-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/save
        method: post
        cors: true
        authorizer: authorizer

AddComment:
  handler: src/presentation/handlers/video/addComment.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddComment-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/comment
        method: post
        cors: true
        authorizer: authorizer

AddLikeToComment:
  handler: src/presentation/handlers/video/addLikeToComment.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddLikeToComment-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/comment/like
        method: post
        cors: true
        authorizer: authorizer

AddWatchedToVideo:
  handler: src/presentation/handlers/video/addWatchedToVideo.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddWatchedToVideo-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
        - sqs:*
      Resource: "*"
  events:
    - http:
        path: video/watched
        method: post
        cors: true
        authorizer: authorizer


GetVideos:
  handler: src/presentation/handlers/video/getVideos.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetVideos-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video
        method: get
        cors: true
        authorizer: authorizer

GetLikedVideos:
  handler: src/presentation/handlers/video/getLikedVideos.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetLikedVideos-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/likedBy/{userId}
        method: get
        cors: true
        authorizer: authorizer

GetSavedVideos:
  handler: src/presentation/handlers/video/getSavedVideos.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetSavedVideos-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/savedBy/{userId}
        method: get
        cors: true
        authorizer: authorizer

GetVideosBySubject:
  handler: src/presentation/handlers/video/getBySubject.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetVideosBySubject-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/subject/{subject}
        method: get
        cors: true
        authorizer: authorizer

GetVideoById:
  handler: src/presentation/handlers/video/getVideoById.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetVideoById-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/get/base/{id}
        method: get
        cors: true
        authorizer: authorizer

GetVideoWithStats:
  handler: src/presentation/handlers/video/getVideoWithStats.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetVideoWithStats-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/get/withStats/{id}
        method: get
        cors: true
        authorizer: authorizer

DeleteVideo:
  handler: src/presentation/handlers/video/deleteVideo.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-DeleteVideo-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - s3:DeleteObject
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/{videoId}
        method: delete
        cors: true
        authorizer: authorizer

RemoveCommentLike:
  handler: src/presentation/handlers/video/removeCommentLike.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-RemoveCommentLike-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/comment/like
        method: delete
        cors: true
        authorizer: authorizer

DeleteComment:
  handler: src/presentation/handlers/video/deleteComment.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-DeleteComment-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/comment
        method: delete
        cors: true
        authorizer: authorizer

RemoveVideoLike:
  handler: src/presentation/handlers/video/removeVideoLike.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-RemoveVideoLike-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/like
        method: delete
        cors: true
        authorizer: authorizer

RemoveVideoSave:
  handler: src/presentation/handlers/video/removeVideoSave.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-RemoveVideoSave-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/save
        method: delete
        cors: true
        authorizer: authorizer

GetCommentsByGroup:
  handler: src/presentation/handlers/video/getCommentsByGroup.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetCommentsByGroup-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/comments/group/{groupId}
        method: get
        cors: true
        authorizer: authorizer

GetCommentsByGroupsBatch:
  handler: src/presentation/handlers/video/getCommentsByGroupsBatch.handler
  memorySize: 1024
  timeout: 30
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetCommentsByGroupsBatch-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/comments/groups/batch
        method: post
        cors: true
        authorizer: authorizer

GetCommentInfo:
  handler: src/presentation/handlers/video/getCommentInfo.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetCommentInfo-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
      Resource: "*"
  events:
    - http:
        path: video/comment/{commentId}/info
        method: get
        cors: true
        authorizer: authorizer

RespondToDoubt:
  handler: src/presentation/handlers/video/respondToDoubt.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-RespondToDoubt-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/comment/{commentId}/respond
        method: post
        cors: true
        authorizer: authorizer