Resources:
  VideoBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: ${self:service}-${self:custom.stage}-saps-s3-videos
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - '*'
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
            AllowedOrigins:
              - '*'
            MaxAge: 3000
  # First deploy VideoBucket.
  # VideoBucketPolicy:
  #   Type: AWS::S3::BucketPolicy
  #   DependsOn:
  #     - VideoBucket
  #   Properties:
  #     Bucket: ${self:service}-${self:custom.stage}-saps-s3-videos
  #     PolicyDocument:
  #       Version: '2012-10-17'
  #       Statement:
  #         - Effect: Allow
  #           Principal: '*'
  #           Action:
  #             - s3:GetObject
  #             - s3:PutObject
  #             - s3:DeleteObject
  #           Resource: 
  #             - arn:aws:s3:::${self:service}-${self:custom.stage}-saps-s3-videos/*
  #             - arn:aws:s3:::${self:service}-${self:custom.stage}-saps-s3-videos
