CreateTeacher:
  handler: src/presentation/handlers/teacher/createTeacher.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-CreateTeacher-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: teacher
        method: post
        cors: true
        authorizer: authorizer 