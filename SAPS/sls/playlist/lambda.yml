CreatePlaylist:
  handler: src/presentation/handlers/playlist/createPlaylist.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-CreatePlaylist-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: playlist
        method: post
        cors: true
        authorizer: authorizer

UpdatePlaylist:
  handler: src/presentation/handlers/playlist/updatePlaylist.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-UpdatePlaylist-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: playlist/update/{playlistId}
        method: post
        cors: true
        authorizer: authorizer

AddVideosToPlaylist:
  handler: src/presentation/handlers/playlist/addVideoToPlaylist.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-AddVideosToPlaylist-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: playlist/addVideos/{playlistId}
        method: post
        cors: true
        authorizer: authorizer

GetPlaylist:
  handler: src/presentation/handlers/playlist/getPlaylist.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetPlaylist-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: playlist/get/{playlistId}
        method: get
        cors: true
        authorizer: authorizer

GetSinglePlaylistByFilter:
  handler: src/presentation/handlers/playlist/getSinglePlaylistByFilter.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetSinglePlaylistByFilter-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: playlist/filter/single
        method: get
        cors: true
        authorizer: authorizer

GetPlaylistsByFilter:
  handler: src/presentation/handlers/playlist/getPlaylistsByFilter.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetPlaylistsByFilter-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: playlist/filter
        method: get
        cors: true
        authorizer: authorizer

GetPlaylistByType:
  handler: src/presentation/handlers/playlist/getPlaylistByType.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetPlaylistByType-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: playlist/type/{type}
        method: get
        cors: true
        authorizer: authorizer

GetPlaylistByOwner:
  handler: src/presentation/handlers/playlist/getPlaylistByOwner.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetPlaylistByOwner-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: playlist/user/{ownerId}
        method: get
        cors: true
        authorizer: authorizer

DeletePlaylist:
  handler: src/presentation/handlers/playlist/deletePlaylist.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-DeletePlaylist-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: playlist/{playlistId}
        method: delete
        cors: true
        authorizer: authorizer 