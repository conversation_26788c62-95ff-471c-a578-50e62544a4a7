import { createTeacherService } from '../src/application/services/teacher/createTeacherService.js';
import { createVideoService } from '../src/application/services/video/createVideoService.js';
import { createPlaylistService } from '../src/application/services/playlist/createPlaylistService.js';
import addVideoToPlaylistService from '../src/application/services/playlist/addVideoToPlaylistService.js';
import fs from 'fs';
import csv from 'csv-parser';
import { createReadStream } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Helper function to parse string arrays like "['a','b',...]"
const parseStringArray = (str) => {
    if (!str) return [];
    try {
        // Remove square brackets and split by comma
        return str.replace(/[\[\]']/g, '').split(',').map(item => item.trim());
    } catch (error) {
        console.error('Error parsing string array:', str);
        return [];
    }
};

// Helper function to parse number arrays like "[1,2,...]"
const parseNumberArray = (str) => {
    if (!str) return [];
    try {
        // Remove square brackets and split by comma
        return str.replace(/[\[\]]/g, '').split(',').map(num => parseInt(num.trim()));
    } catch (error) {
        console.error('Error parsing number array:', str);
        return [];
    }
};

const processCSV = (filePath) => {
    return new Promise((resolve, reject) => {
        const results = [];
        createReadStream(filePath)
            .pipe(csv({ separator: '\t' }))
            .on('data', (data) => results.push(data))
            .on('end', () => resolve(results))
            .on('error', (error) => reject(error));
    });
};

async function generatePlaylists() {
    try {
        // Read CSV files
        const teachers = await processCSV(path.join(__dirname, 'teachers.csv'));
        const videos = await processCSV(path.join(__dirname, 'videos.csv'));
        const playlists = await processCSV(path.join(__dirname, 'playlists.csv'));

        // console.log('teachers',teachers);
        // console.log('videos',videos);
        // console.log('playlists',playlists);

        // Create teachers
        const teacherMap = new Map();
        for (const teacher of teachers) {
            const newTeacher = await createTeacherService({
                name: teacher.name,
                description: teacher.description || '',
                awards: parseStringArray(teacher.awards)
            });
            teacherMap.set(teacher.id, newTeacher.id);
        }

        console.log('teacherMap',teacherMap);

        // Create videos with proper teacher connection
        const videoMap = new Map();
        for (const video of videos) {
            const teacherId = teacherMap.get(video.teacherId.toString());
            if (!teacherId) {
                console.error(`Teacher not found for video: ${video.title}`);
                continue;
            }

            const newVideo = await createVideoService({
                title: video.title,
                teacherId: teacherId,
                videoUrl: video.videoUrl,
                subject: video.subject,
                description: video.description || '',
                thumbnail: video.thumbnail || '',
                tags: parseStringArray(video.tags),
                levelRange: parseNumberArray(video.levelRange)
            });
            videoMap.set(video.id, newVideo.id);
        }

        console.log('videoMap',videoMap);

        // Create playlists and add videos
        for (const playlist of playlists) {
            const videoIds = parseNumberArray(playlist.VideoIds).map(id => videoMap.get(id.toString()));
            console.log('videoIds',videoIds);
            
            const newPlaylist = await createPlaylistService({
                title: playlist.title,
                subject: parseStringArray(playlist.subject),
                type: 'GLOBAL',
                ownerId: 'atomize',
                olympiads: parseStringArray(playlist.olympiadId),
                levelRange: parseNumberArray(playlist.levelRange)
            });

            console.log('newPlaylist',newPlaylist);

            if (videoIds.length > 0) {
                await addVideoToPlaylistService({
                    playlistId: newPlaylist.id,
                    videosIds: videoIds
                });
            }
        }

        console.log('Successfully generated playlists with videos and teachers');
    } catch (error) {
        console.error('Error generating playlists:', error);
        throw error;
    }
}

// Run the generation
generatePlaylists(); 