const path = require("path");
const slsw = require("serverless-webpack");
const CopyWebpackPlugin = require("copy-webpack-plugin");

module.exports = {
  entry: slsw.lib.entries,
  target: "node",
  devtool: "source-map", // Generate sourcemaps for proper error messages
  externals: {}, // Exclude all node dependencies
  mode: slsw.lib.webpack.isLocal ? "development" : "production",
  optimization: {
    minimize: false, // We do not want to minimize our code
  },
  performance: {
    hints: false, // Turn off size warnings for entry points
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        loader: "babel-loader",
        include: __dirname,
        exclude: /node_modules/,
        options: {
          presets: ["@babel/preset-env"],
        },
      },
    ],
  },
  plugins: [
    new CopyWebpackPlugin({
      patterns: [
        {
          from: "src/domain/models/generated/client", // Copy prisma binaries
          to: "src/domain/models/generated/client",
        },
      ],
    }),
  ],
  resolve: {
    fallback: {
      fs: false,
      path: false,
    },
  },
};
