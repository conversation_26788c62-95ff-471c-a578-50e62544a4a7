// // eslint.config.js
// import js from '@eslint/js';
// import eslintPluginImport from 'eslint-plugin-import';

// export default [
//     js.configs.recommended,
//     {
//         files: ['**.js'],
//         plugins: {
//             import: eslintPluginImport,
//         },
//         languageOptions: {
//             ecmaVersion: 12,
//             sourceType: 'module',
//             globals: {
//                 browser: true,
//                 node: true,
//                 console: true, // Allow use of `console` globally'
//                 process: true
//             },
//         },
//         rules: {
//             // Best Practices
//             'eqeqeq': ['error', 'always'],
//             'curly': ['error', 'multi-line'],
//             'default-case': 'warn',
//             'dot-notation': 'warn',

//             // Style
//             'indent': ['error', 4, { SwitchCase: 1 }], // Set indentation to 4 spaces

//             // Variables
//             'no-unused-vars': ['warn', { args: 'none', ignoreRestSiblings: true }],
//             'no-use-before-define': ['error', { functions: false, classes: true }],
//             'no-undef': 'off', // Disable no-undef for all files

//             // Import Rules
//             'import/no-unresolved': ['error', { commonjs: false, caseSensitive: true }], // Change commonjs to false
//             'import/no-duplicates': 'warn',
//             'import/extensions': [
//                 'error',
//                 'ignorePackages',
//                 {
//                     js: 'always',   // Require .js extensions
//                     jsx: 'always',  // Require .jsx extensions (if using React)
//                     ts: 'always',   // Require .ts extensions (if using TypeScript)
//                     tsx: 'always',  // Require .tsx extensions (if using TypeScript with React)
//                 },
//             ],

//             // Code Complexity
//             'complexity': ['warn', { max: 10 }],
//             'max-depth': ['warn', 4],

//         },
//         settings: {
//             'import/resolver': {
//                 node: {
//                     extensions: ['.js', '.jsx', '.json'],
//                 },
//             },
//         },
//     },
//     {
//         files: ['**/*.test.js', '**/*.spec.js'], // Adjust according to your test file patterns
//         languageOptions: {
//             globals: {
//                 describe: true,
//                 test: true,
//                 it: true,
//                 expect: true,
//                 beforeEach: true,
//                 afterEach: true,
//                 beforeAll: true,
//                 afterAll: true,
//                 jest: true, // Allow Jest's global variable
//                 browser: true,
//                 node: true,
//                 console: true, // Allow use of `console` globally'
//                 process: true
//             },
//         },
//     },
// ];