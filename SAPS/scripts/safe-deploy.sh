#!/bin/bash

# Safe deployment script with rollback capabilities
# Usage: ./scripts/safe-deploy.sh [stage] [--force]

set -e

# Configuration
STAGE="${1:-dev}"
FORCE_FLAG="$2"
STACK_NAME="saps-service-${STAGE}"
PROFILE="atomize-dev"
REGION="us-east-2"
BACKUP_DIR="./deployment-backups"

echo "🚀 Safe deployment script for $STACK_NAME"
echo "Stage: $STAGE"
echo "Profile: $PROFILE"
echo "Region: $REGION"
echo ""

# Create backup directory
mkdir -p $BACKUP_DIR

# Function to create stack backup
create_backup() {
    echo "📦 Creating stack backup..."
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_FILE="$BACKUP_DIR/${STACK_NAME}_${TIMESTAMP}.json"
    
    if aws cloudformation describe-stacks \
        --stack-name $STACK_NAME \
        --profile $PROFILE \
        --region $REGION \
        --output json > "$BACKUP_FILE" 2>/dev/null; then
        echo "✅ Stack backup created: $BACKUP_FILE"
    else
        echo "⚠️  No existing stack to backup"
    fi
}

# Function to monitor deployment
monitor_deployment() {
    echo "👀 Monitoring deployment progress..."
    
    # Wait for stack operation to complete
    while true; do
        STATUS=$(aws cloudformation describe-stacks \
            --stack-name $STACK_NAME \
            --profile $PROFILE \
            --region $REGION \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "UNKNOWN")
        
        echo "Current status: $STATUS"
        
        case $STATUS in
            "CREATE_COMPLETE"|"UPDATE_COMPLETE")
                echo "✅ Deployment successful!"
                return 0
                ;;
            "CREATE_FAILED"|"UPDATE_FAILED"|"UPDATE_ROLLBACK_COMPLETE"|"UPDATE_ROLLBACK_FAILED")
                echo "❌ Deployment failed with status: $STATUS"
                return 1
                ;;
            "CREATE_IN_PROGRESS"|"UPDATE_IN_PROGRESS"|"UPDATE_ROLLBACK_IN_PROGRESS")
                echo "⏳ Deployment in progress..."
                sleep 30
                ;;
            *)
                echo "⚠️  Unknown status: $STATUS"
                sleep 30
                ;;
        esac
    done
}

# Function to show recent stack events on failure
show_failure_details() {
    echo ""
    echo "🔍 Recent stack events (last 10):"
    aws cloudformation describe-stack-events \
        --stack-name $STACK_NAME \
        --profile $PROFILE \
        --region $REGION \
        --query 'StackEvents[:10].[Timestamp,LogicalResourceId,ResourceStatus,ResourceStatusReason]' \
        --output table 2>/dev/null || echo "Could not retrieve stack events"
}

# Run pre-deployment checks unless forced
if [ "$FORCE_FLAG" != "--force" ]; then
    echo "🔍 Running pre-deployment checks..."
    if ! ./scripts/pre-deploy-check.sh $STAGE; then
        echo "❌ Pre-deployment checks failed"
        echo "💡 Use --force flag to skip checks (not recommended)"
        exit 1
    fi
    echo ""
fi

# Create backup of existing stack
create_backup

# Start deployment
echo "🚀 Starting deployment..."
echo "Command: serverless deploy --stage $STAGE --verbose"
echo ""

# Deploy with error handling
if serverless deploy --stage $STAGE --verbose; then
    echo ""
    echo "✅ Serverless deploy command completed"
    
    # Monitor the actual CloudFormation deployment
    if monitor_deployment; then
        echo ""
        echo "🎉 Deployment completed successfully!"
        echo "📊 Stack outputs:"
        aws cloudformation describe-stacks \
            --stack-name $STACK_NAME \
            --profile $PROFILE \
            --region $REGION \
            --query 'Stacks[0].Outputs' \
            --output table 2>/dev/null || echo "No outputs available"
    else
        echo ""
        echo "❌ Deployment monitoring detected failure"
        show_failure_details
        exit 1
    fi
else
    echo ""
    echo "❌ Serverless deploy command failed"
    show_failure_details
    exit 1
fi

echo ""
echo "🏁 Deployment process completed"
