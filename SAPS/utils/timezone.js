/**
 * Timezone utility functions
 */

/**
 * Gets a date string for a specific timezone/region
 * @param {string} timezone - IANA timezone (e.g. 'America/New_York', 'Asia/Tokyo')
 * @param {Date} [date] - Optional date object, defaults to current date/time
 * @returns {string} Formatted date string for the specified timezone
 */
export const getRegionDate = (timezone, date = new Date()) => {
  return date.toLocaleString("en-US", { timeZone: timezone });
};

/**
 * Gets the timezone offset in minutes for a specific timezone
 * @param {string} timezone - IANA timezone
 * @returns {number} Timezone offset in minutes
 */
export const getTimezoneOffset = (timezone) => {
  const date = new Date();
  const options = { timeZone: timezone, timeZoneName: "short" };
  const timeString = date.toLocaleString("en-US", options);
  const tzString = timeString.split(" ").pop();
  const offset =
    Date.parse(`2020-01-01T00:00:00${tzString}`) -
    Date.parse("2020-01-01T00:00:00Z");
  return offset / (60 * 1000);
};

/**
 * Converts a date to a different timezone
 * @param {Date} date - Date object to convert
 * @param {string} timezone - Target IANA timezone
 * @returns {Date} New date object in the target timezone
 */
export const convertToTimezone = (date, timezone) => {
  return new Date(date.toLocaleString("en-US", { timeZone: timezone }));
};

/**
 * Checks if a timezone string is valid
 * @param {string} timezone - IANA timezone to validate
 * @returns {boolean} Whether the timezone is valid
 */
export const isValidTimezone = (timezone) => {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch (e) {
    return false;
  }
};

/**
 * Gets the current time for a specific timezone, defaults to Brasilia
 * @param {string} [timezone='America/Sao_Paulo'] - IANA timezone
 * @returns {Date} Current date object in the specified timezone
 */
export const getCurrentTime = (timezone = "America/Sao_Paulo") => {
  return new Date(new Date().toLocaleString("en-US", { timeZone: timezone }));
};
