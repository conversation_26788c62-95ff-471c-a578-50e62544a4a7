// import { LambdaClient, InvokeCommand } from "@aws-sdk/client-lambda";

// const lambdaClient = new LambdaClient({
//   region: "us-east-2",
// });

// export async function invokeLambda(lambdaName, payload) {
//   const serverlessFormattedLambdaName = `${process.env.SERVICE}-${process.env.STAGE}-${lambdaName}`;

//   if (!process.env.AWS_ACCOUNT_ID || process.env.AWS_ACCOUNT_ID.length !== 12) {
//     throw new Error("AWS_ACCOUNT_ID must be a valid 12-digit AWS account ID.");
//   }

//   console.info("Invoking Lambda function:");
//   console.info(" - Target Lambda: ", serverlessFormattedLambdaName);
//   console.info(" - Payload: ", payload);

//   const body = { body: payload };

//   try {
//     const command = new InvokeCommand({
//       FunctionName: serverlessFormattedLambdaName,
//       Payload: Buffer.from(JSON.stringify(body)),
//     });

//     const response = await lambdaClient.send(command);
//     console.info(
//       "Lambda invoked successfully:",
//       JSON.stringify(response, null, 2)
//     );
//     return response;
//   } catch (error) {
//     console.error("Error invoking Lambda function:", error);
//     throw error;
//   }
// }
