import {
  SQSClient,
  SendMessageCommand,
  ReceiveMessageCommand,
} from "@aws-sdk/client-sqs";
import { v4 as uuidv4 } from "uuid";

console.log("REGION: ", process.env.REGION);
const sqsClient = new SQSClient({ region: process.env.REGION });
console.log("SQS CLIENT: ", sqsClient);

export async function sendMessageToQueue(
  queueName,
  messageBody,
  isFifo = false,
  messageGroupId = null
) {
  console.log("SENDING MESSAGE TO QUEUE: ", {
    queueName,
    messageBody,
    isFifo,
    messageGroupId,
  });
  const queueUrl = `${process.env.BASE_SQS_QUEUE_URL}${queueName}`;
  console.log("QUEUE URL: ", queueUrl);
  const params = {
    QueueUrl: queueUrl,
    MessageBody: JSON.stringify(messageBody),
  };

  if (isFifo) {
    if (!messageGroupId) {
      throw new Error("MessageGroupId is required for FIFO queues");
    }
    params.MessageGroupId = messageGroupId;
    params.MessageDeduplicationId = uuidv4();
  }

  const command = new SendMessageCommand(params);
  const response = await sqsClient.send(command);
  console.log("Message sent successfully:", response);
  return response;
}

export async function checkRemainingMessages(queueName, messageGroupId) {
  const queueUrl = `${process.env.BASE_SQS_QUEUE_URL}${queueName}`;
  const params = {
    QueueUrl: queueUrl,
    MaxNumberOfMessages: 10,
    MessageAttributeNames: ["All"],
    WaitTimeSeconds: 0,
  };

  const command = new ReceiveMessageCommand(params);
  const response = await sqsClient.send(command);

  const remainingMessages = response.Messages?.filter(
    (msg) => msg.Attributes.MessageGroupId === messageGroupId
  );

  console.log("remainingMessages: ", remainingMessages);

  return remainingMessages && remainingMessages.length > 0;
}

// const formatFifoMessage = (body, messageGroupId) => {
//   const id = uuidv4();

//   return {
//     Id: id,
//     MessageBody: JSON.stringify(body),
//     MessageGroupId: messageGroupId,
//     MessageDeduplicationId: id,
//   };
// };