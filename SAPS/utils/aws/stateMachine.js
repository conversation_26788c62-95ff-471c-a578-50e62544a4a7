import { SF<PERSON>lient, StartExecutionCommand, SendTaskSuccessCommand, SendTaskFailureCommand } from "@aws-sdk/client-sfn";

const sfnClient = new SFNClient({ region: process.env.REGION });

export async function startExecution(stateMachineName, input) {
  try {
    const stateMachineArn = process.env.BASE_STATE_MACHINE_ARN.concat(stateMachineName);
    console.log("stateMachineArn", stateMachineArn);
    const params = {
      stateMachineArn,
      input: JSON.stringify(input),
    };
    console.log("params", params);
    const command = new StartExecutionCommand(params);
    const response = await sfnClient.send(command);
    console.log("Execution started successfully:", response);
    return response;
  } catch (error) {
    console.error("Error starting execution:", error);
    throw error;
  }
}

export async function sendTaskSuccess(taskToken, output) {
  try {
    const params = {
      taskToken,
      output: JSON.stringify(output),
    };
    const command = new SendTaskSuccessCommand(params);
    const response = await sfnClient.send(command);
    console.log("Task success sent successfully:", response);
    return response;
  } catch (error) {
    console.error("Error sending task success:", error);
    throw error;
  }
}

export async function sendTaskFailure(taskToken, error) {
  try {
    const params = {
      taskToken,
      error: JSON.stringify(error),
    };
    const command = new SendTaskFailureCommand(params);
    const response = await sfnClient.send(command);
    console.log("Task failure sent successfully:", response);
    return response;
  } catch (error) {
    console.error("Error sending task failure:", error);
    throw error;
  }
}

// Add other relevant utilities related to state machines as needed. 