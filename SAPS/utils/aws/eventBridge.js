import { SchedulerClient, CreateScheduleCommand } from "@aws-sdk/client-scheduler";

const scheduler = new SchedulerClient({
  region: "us-east-2",
});

export async function scheduleLambda(lambdaName, scheduleDate, payload) {
  const timeOffset = -180; // São Paulo offset
  const saoPauloDate = new Date(scheduleDate);
  const utcDate = new Date(saoPauloDate.getTime() - timeOffset * 60000);

  const serverlessFormattedLambdaName = `${process.env.SERVICE}-${process.env.STAGE}-${lambdaName}`;
  const scheduleName = `${serverlessFormattedLambdaName}-${Date.now()}`;

  if (!process.env.AWS_ACCOUNT_ID || process.env.AWS_ACCOUNT_ID.length !== 12) {
    throw new Error("AWS_ACCOUNT_ID must be a valid 12-digit AWS account ID.");
  }

  console.info("Creating schedule:");
  console.info(" - Schedule Name: ", scheduleName);
  console.info(" - Schedule Time: ", utcDate.toISOString());
  console.info(" - Target Lambda: ", serverlessFormattedLambdaName);
  console.info(" - Payload: ", payload);
  console.log("AWS-id: ", process.env.AWS_ACCOUNT_ID)

  try {
    const command = new CreateScheduleCommand({
      Name: scheduleName,
      ScheduleExpression: `at(${utcDate.toISOString().slice(0, 19)})`,
      FlexibleTimeWindow: { Mode: "OFF" },
      Target: {
        Arn: `arn:aws:lambda:us-east-2:${process.env.AWS_ACCOUNT_ID}:function:${serverlessFormattedLambdaName}`,
        RoleArn: `arn:aws:iam::${process.env.AWS_ACCOUNT_ID}:role/${process.env.SERVICE}-${process.env.STAGE}-scheduler-role`,
        Input: JSON.stringify(payload),
      },
    });

    const response = await scheduler.send(command);
    console.info("Schedule created successfully:", JSON.stringify(response, null, 2));
    return response;
  } catch (error) {
    console.error("Error scheduling event:", error);
    throw error;
  }
}
