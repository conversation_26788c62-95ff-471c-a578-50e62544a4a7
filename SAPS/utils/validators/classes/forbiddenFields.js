import { Validator } from "../interfaces.js";

export class ForbiddenFieldsValidator extends Validator {
    constructor(allowedFields) {
        super();
        this.allowedFields = allowedFields;
    }

    validate(data) {
        for (const key in data) {
            if (!this.allowedFields.includes(key)) {
                throw new Error(`Field '${key}' is not allowed`);
            }
        }
        return true;
    }
}