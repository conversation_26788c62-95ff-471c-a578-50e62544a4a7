import { Validator } from "../interfaces.js";

export class MissingInputsValidator extends Validator {
    validate(data) {
        const missingKeys = [];

        // Check if any object property is undefined or null
        Object.keys(data).forEach(key => {
            if (data[key] === undefined) {
                missingKeys.push(key);
            }
        });

        if (missingKeys.length > 0) {
            throw new Error(`Missing or undefined keys: ${missingKeys.join(", ")}`);
        }

        return true; // If no missing keys
    }
}
