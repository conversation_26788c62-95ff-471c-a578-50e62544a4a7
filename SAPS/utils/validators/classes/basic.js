import { Validator } from "../interfaces.js";

// RangeValidator: checks if the numeric values are within a specified range
export class RangeValidator extends Validator {
    constructor(min, max) {
        super();
        this.min = min;
        this.max = max;
    }

    validate(data) {
        for (const key in data) {
            const value = data[key];
            if (typeof value === 'number' && (value < this.min || value > this.max)) {
                throw new Error(`Value for ${key} is out of range: ${value}`);
            }
        }
        return true;
    }
}

// TypeValidator: checks if the values match the expected type
export class TypeValidator extends Validator {
    constructor(expectedTypes) {
        super();
        this.expectedTypes = expectedTypes;
    }

    validate(data) {
        for (const key in data) {
            const expectedType = this.expectedTypes[key];
            if (expectedType && typeof data[key] !== expectedType) {
                throw new Error(`Invalid type for ${key}: expected ${expectedType}, got ${typeof data[key]}`);
            }
        }
        return true;
    }
}

// StringLengthValidator: checks if string lengths are within the specified limits
export class StringLengthValidator extends Validator {
    constructor(minLength, maxLength) {
        super();
        this.minLength = minLength;
        this.maxLength = maxLength;
    }

    validate(data) {
        for (const key in data) {
            const value = data[key];
            if (typeof value === 'string' && (value.length < this.minLength || value.length > this.maxLength)) {
                throw new Error(`String length for ${key} is out of range: ${value.length}`);
            }
        }
        return true;
    }
}

export class EmailValidator extends Validator {
    validate(data) {
        const email = data.email; // Assuming the email is stored under the 'email' key
        if (!email) {
            throw new Error("Email is required");
        }

        // Regular expression for validating an Email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new Error("Invalid email format");
        }

        return true; // If the email is valid
    }
}