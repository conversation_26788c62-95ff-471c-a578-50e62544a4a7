import { Validator } from './interfaces.js';
import { ValidationError } from '../customErrors/index.js';

class ValidatorDecorator extends Validator {
    constructor(validator) {
        super();
        this.validator = validator;
    }

    validate(data) {
        if (this.validator) {
            try {
                this.validator.validate(data);
            } catch (error) {
                // Throw a ValidationError if a validation error occurs
                throw new ValidationError(error.message);
            }
        }
        return true;
    }
}

export default class CompositeValidator extends ValidatorDecorator {
    constructor(validator) {
        super(validator); // Call the base class constructor
        this.nextValidator = null;
    }

    setNext(validator) {
        this.nextValidator = validator;
    }

    validate(data) {
        // Validate with the current validator
        super.validate(data);

        // Pass the data to the next validator in the chain, if any
        if (this.nextValidator) {
            try {
                this.nextValidator.validate(data);
            } catch (error) {
                // Throw a ValidationError if a validation error occurs
                throw new ValidationError(error.message);
            }
        }
        return true;
    }
}

export * from './classes/index.js';