export class CustomError extends Error {
    constructor(message, code) {
        super(message);
        this.name = this.constructor.name; // Set the error name to the class name
        this.code = code; // Custom error code
        Error.captureStackTrace(this, this.constructor);
    }
}

export class NotFoundError extends CustomError {
    constructor(message = 'Resource not found') {
        super(`NOT_FOUND: ${message}`, 404); // Set a composite message and HTTP status code
    }
}

export class ValidationError extends CustomError {
    constructor(message = 'Invalid input data') {
        super(`VALIDATION_ERROR: ${message}`, 400); // Set a composite message and HTTP status code
    }
}

export class AuthenticationError extends CustomError {
    constructor(message = 'Authentication failed') {
        super(`AUTHENTICATION_ERROR: ${message}`, 401); // Set a composite message and HTTP status code
    }
}

export class AuthorizationError extends CustomError {
    constructor(message = 'Not authorized') {
        super(`AUTHORIZATION_ERROR: ${message}`, 403); // Set a composite message and HTTP status code
    }
}

export class DatabaseError extends CustomError {
    constructor(message = 'Database operation failed') {
        super(`DATABASE_ERROR: ${message}`, 500); // Set a composite message and HTTP status code
    }
}