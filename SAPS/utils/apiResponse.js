export const apiResponse = (status, { header, body }) => {
  const finalHeaders = Object.assign(
    {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true,
      "Access-Control-Allow-Headers":
        "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
    },
    header
  );

  return {
    statusCode: status,
    headers: finalHeaders,
    body: JSON.stringify(body),
  };
};

// Helper functions for common response patterns
export const successResponse = (data, statusCode = 200) => ({
  statusCode,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Credentials': true,
  },
  body: JSON.stringify({
    success: true,
    data
  })
});

export const errorResponse = (message, statusCode = 500, details = null) => ({
  statusCode,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Credentials': true,
  },
  body: JSON.stringify({
    success: false,
    error: message,
    details
  })
});
