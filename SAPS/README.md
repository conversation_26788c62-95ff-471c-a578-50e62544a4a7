# SAPS
Stream And Play Service for Atomize vídeo related services

## Description

SAPS is composed of both streaming and video playing services for XXXX, concerning the features of lives, lessons, and their marginal functionalities like comments, likes, and playlists.

## Table of Contents

- [SAPS](#saps)
  - [Description](#description)
  - [Table of Contents](#table-of-contents)
  - [Abstract](#abstract)
  - [Installation](#installation)
    - [Prerequisites](#prerequisites)
    - [Steps](#steps)
  - [Usage](#usage)
    - [run service](#run-service)
    - [prisma](#prisma)
  - [Manual Deploy](#manual-deploy)

## Abstract

This project follows the principles of Domain-Driven Design (DDD), focusing on creating a robust and maintainable architecture. DDD emphasizes the importance of the domain, which represents the core business logic, and organizes the codebase into distinct layers:

- **Domain Layer**: Contains the business logic, domain models, aggregates, and value objects. It is the heart of the application where the core business rules reside.
- **Application Layer**: Acts as an intermediary between the presentation and domain layers. It handles application logic, orchestrates tasks, and manages use cases.
- **Presentation Layer**: Responsible for handling HTTP requests and responses, user interactions, and UI logic. It includes controllers and routes that interact with the application layer.
- **Infrastructure Layer**: Manages external dependencies such as databases, messaging systems, and third-party services. It provides implementations for repositories, external APIs, and other infrastructure concerns.

This layered architecture promotes separation of concerns, making the codebase more modular, testable, and maintainable.

## Installation

### Prerequisites

- Node.js >= v14.x
- MongoDB
- AWS CLI

### Steps

1. Install dependencies
   ```sh
   npm install
   ```
2. Set up environment variables
   ```sh Create a .env file in the root directory and add the required environment variables:
   DATABASE_URL: RDS url for prisma connection
   ```
## Usage

### run service
   ```sh 
    npm run startdev
   ```

### prisma
   ```sh 
    npm run prisma:migrate : create a migration file and update the tables from your local database
    npm run prisma:generate : update @prisma and .prisma to handle the prisma schemas
    npm run prisma:deploy : create a migration file and update the tables from the RDS database
    npm run prisma:studio : host prisma studio on localhost
   ```
   
## Manual Deploy