#!/bin/bash

# Script para iniciar o frontend do projeto Atomize
# Execute este script a partir da pasta raiz do projeto

echo "🚀 Iniciando o frontend do Atomize..."

# Verifica se estamos na pasta raiz correta
if [ ! -d "front-end" ]; then
    echo "❌ Erro: Pasta 'front-end' não encontrada. Certifique-se de estar na pasta raiz do projeto."
    exit 1
fi

# Navega para a pasta do frontend
cd front-end

# Verifica se o package.json existe
if [ ! -f "package.json" ]; then
    echo "❌ Erro: package.json não encontrado na pasta front-end."
    exit 1
fi

# Verifica se node_modules existe, se não, instala as dependências
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências..."
    npm install
fi

# Inicia o servidor de desenvolvimento
echo "🌐 Iniciando servidor de desenvolvimento Next.js..."
npm run dev